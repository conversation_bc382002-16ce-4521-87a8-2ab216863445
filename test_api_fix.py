#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试部门异常统计API修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import app
import json

def test_person_statistics_api():
    """测试责任人统计API返回的字段名"""
    print("=== 测试部门异常统计API修复 ===\n")

    # 直接测试API函数，绕过Flask登录验证
    from applications.view.system.exception_cost_statistics import api_person_statistics
    from flask import Flask
    from unittest.mock import patch

    # 创建应用上下文
    with app.app_context():
        with app.test_request_context('/system/exception_cost_statistics/api/person_statistics'):
            # 模拟登录用户和权限
            with patch('flask_login.current_user') as mock_user:
                mock_user.is_authenticated = True
                mock_user.id = 1
                mock_user.username = 'admin'

                with patch('applications.common.utils.rights.authorize') as mock_authorize:
                    # 让权限装饰器直接通过
                    def mock_decorator(permission, log=False):
                        def decorator(func):
                            return func
                        return decorator
                    mock_authorize.side_effect = mock_decorator

                    try:
                        # 调用API函数
                        response = api_person_statistics()

                        print("✅ API调用成功")

                        # 解析JSON响应
                        if hasattr(response, 'get_json'):
                            data = response.get_json()
                        else:
                            # 如果是直接返回的Response对象
                            import json as json_module
                            data = json_module.loads(response.data.decode('utf-8'))

                        print(f"响应数据结构: {json.dumps(data, indent=2, ensure_ascii=False)}")

                        if data.get('code') == 0:
                            print("✅ API返回成功")

                            # 检查返回数据中是否包含responsible_dept_name字段
                            if data.get('data'):
                                first_item = data['data'][0] if data['data'] else {}
                                print(f"\n第一条数据字段: {list(first_item.keys())}")

                                if 'responsible_dept_name' in first_item:
                                    print("✅ responsible_dept_name字段存在")
                                    print(f"   值: {first_item['responsible_dept_name']}")
                                else:
                                    print("❌ responsible_dept_name字段不存在")

                                if 'responsible_person' in first_item:
                                    print("✅ responsible_person字段存在")
                                    print(f"   值: {first_item['responsible_person']}")
                                else:
                                    print("❌ responsible_person字段不存在")
                            else:
                                print("⚠️  返回数据为空")
                        else:
                            print(f"❌ API返回错误: {data.get('msg')}")

                    except Exception as e:
                        print(f"❌ API调用失败: {str(e)}")
                        import traceback
                        traceback.print_exc()

if __name__ == '__main__':
    test_person_statistics_api()
