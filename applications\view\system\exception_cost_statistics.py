from flask import Blueprint, render_template, request, jsonify, send_file, current_app
from flask_login import login_required, current_user
from applications.common.utils.rights import authorize
from applications.common.utils.http import table_api, success_api, fail_api
from applications.extensions import db
from applications.models.quality_exception import QualityException
from applications.models.admin_dept import Dept
from applications.models.admin_yg import ygong
from applications.models.dept_monthly_unit_cost import DeptMonthlyUnitCost
from applications.services.dept_unit_cost_service import DeptUnitCostService
from applications.services.dept_unit_cost_config_service import DeptUnitCostConfigService
from sqlalchemy import func, and_, or_, extract
from datetime import datetime, timedelta
import pandas as pd
import io
import os

bp = Blueprint('exception_cost_statistics', __name__, url_prefix='/exception_cost_statistics')


def get_current_year_range():
    """获取当年的时间范围

    Returns:
        tuple: (当年开始日期字符串, 当年结束日期字符串)
    """
    current_year = datetime.now().year
    start_date = f"{current_year}-01-01"
    end_date = f"{current_year}-12-31"
    return start_date, end_date


@bp.route('/')
@login_required
@authorize("system:exception_cost:main", log=True)
def main():
    """异常成本统计主页面"""
    # 获取所有活跃部门列表供筛选
    depts = Dept.query.filter_by(status=1).order_by(Dept.sort).all()

    return render_template('system/exception_cost_statistics/main.html', depts=depts)


@bp.route('/api/dept_statistics')
@login_required
@authorize("system:exception_cost:view")
def api_dept_statistics():
    """部门异常统计API"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 如果没有提供时间范围，默认使用当年数据
        if not start_date and not end_date:
            start_date, end_date = get_current_year_range()
        
        # 构建查询条件
        query = db.session.query(
            QualityException.responsible_dept_id,
            Dept.dept_name,
            func.count(QualityException.id).label('exception_count'),
            func.sum(QualityException.estimated_hours).label('total_hours'),
            func.avg(QualityException.estimated_hours).label('avg_hours')
        ).outerjoin(
            Dept, QualityException.responsible_dept_id == Dept.id
        ).filter(
            QualityException.sync_status == 1  # 只统计同步成功的数据
        )
        
        # 时间范围筛选（优先使用discovery_time，如果为空则使用create_time）
        if start_date:
            query = query.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )
        if end_date:
            query = query.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )
        
        # 部门筛选
        if dept_id:
            query = query.filter(QualityException.responsible_dept_id == dept_id)
        
        # 移除用户权限筛选，允许查看所有部门数据
        
        # 分组并排序
        results = query.group_by(
            QualityException.responsible_dept_id, Dept.dept_name
        ).order_by(
            func.count(QualityException.id).desc()
        ).all()
        
        # 格式化数据并计算异常成本
        data = []
        total_exceptions = 0
        total_hours = 0
        total_cost = 0

        for result in results:
            dept_id, dept_name, count, hours, avg_hours = result

            # 处理空值
            hours = float(hours) if hours else 0
            avg_hours = float(avg_hours) if avg_hours else 0

            # 计算该部门的异常成本
            dept_cost = 0
            if dept_id and hours > 0:
                # 获取该部门在指定时间范围内的异常记录
                dept_exceptions = db.session.query(QualityException).filter(
                    QualityException.responsible_dept_id == dept_id,
                    QualityException.sync_status == 1
                )

                # 应用时间筛选（优先使用discovery_time，如果为空则使用create_time）
                if start_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time >= start_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time >= start_date
                            )
                        )
                    )
                if end_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time <= end_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time <= end_date
                            )
                        )
                    )

                # 计算每个异常的成本
                for exception in dept_exceptions.all():
                    if exception.estimated_hours and exception.responsible_dept_id:
                        # 使用发现时间，如果为空则使用创建时间
                        discovery_time = exception.discovery_time or exception.create_time
                        if discovery_time:
                            exception_cost = DeptUnitCostService.calculate_exception_cost(
                                dept_id=exception.responsible_dept_id,
                                estimated_hours=exception.estimated_hours,
                                discovery_time=discovery_time
                            )
                            dept_cost += exception_cost

            total_exceptions += count
            total_hours += hours
            total_cost += dept_cost

            data.append({
                'dept_id': dept_id,
                'dept_name': dept_name or '未分配部门',
                'exception_count': count,
                'total_hours': round(hours, 2),
                'avg_hours': round(avg_hours, 2),
                'cost_impact': round(dept_cost, 2)
            })
        
        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'data': data,
            'summary': {
                'total_exceptions': total_exceptions,
                'total_hours': round(total_hours, 2),
                'total_cost': round(total_cost, 2)
            }
        })
        
    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}',
            'data': [],
            'summary': {
                'total_exceptions': 0,
                'total_hours': 0,
                'total_cost': 0
            }
        })


@bp.route('/api/person_statistics')
@login_required
@authorize("system:exception_cost:view")
def api_person_statistics():
    """责任部门异常统计API（按责任人字段分组，实际为部门名称）"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 如果没有提供时间范围，默认使用当年数据
        if not start_date and not end_date:
            start_date, end_date = get_current_year_range()

        # 获取排序参数
        sort_field = request.args.get('sort_field', '')
        sort_order = request.args.get('sort_order', 'desc')  # asc 或 desc

        # 验证排序参数
        valid_sort_fields = ['exception_count', 'total_hours', 'avg_hours', 'cost_impact', 'responsible_person']
        valid_sort_orders = ['asc', 'desc']

        if sort_field and sort_field not in valid_sort_fields:
            sort_field = ''  # 重置为默认
        if sort_order and sort_order.lower() not in valid_sort_orders:
            sort_order = 'desc'  # 重置为默认

        
        # 构建查询条件 - 使用responsible_dept_name字段进行部门统计
        query = db.session.query(
            QualityException.responsible_dept_name,
            QualityException.responsible_person,  # 保留作为回退字段
            QualityException.responsible_dept_id,
            Dept.dept_name.label('matched_dept_name'),
            func.count(QualityException.id).label('exception_count'),
            func.sum(QualityException.estimated_hours).label('total_hours'),
            func.avg(QualityException.estimated_hours).label('avg_hours')
        ).outerjoin(
            Dept, QualityException.responsible_dept_name == Dept.dept_name
        ).filter(
            QualityException.sync_status == 1,  # 只统计同步成功的数据
            or_(
                and_(
                    QualityException.responsible_dept_name.isnot(None),
                    QualityException.responsible_dept_name != ''
                ),
                and_(
                    QualityException.responsible_dept_name.is_(None),
                    QualityException.responsible_person.isnot(None),
                    QualityException.responsible_person != ''
                )
            )
        )
        
        # 时间范围筛选（优先使用discovery_time，如果为空则使用create_time）
        if start_date:
            query = query.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )
        if end_date:
            query = query.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )
        
        # 部门筛选
        if dept_id:
            query = query.filter(QualityException.responsible_dept_id == dept_id)
        
        # 移除用户权限筛选，允许查看所有部门数据
        
        # 分组 - 使用responsible_dept_name作为主要分组字段
        query = query.group_by(
            QualityException.responsible_dept_name,
            QualityException.responsible_person,
            QualityException.responsible_dept_id,
            Dept.dept_name
        )

        # 根据排序参数构建排序条件
        if sort_field and sort_order:
            if sort_field == 'exception_count':
                sort_column = func.count(QualityException.id)
            elif sort_field == 'total_hours':
                sort_column = func.sum(QualityException.estimated_hours)
            elif sort_field == 'avg_hours':
                sort_column = func.avg(QualityException.estimated_hours)
            elif sort_field == 'cost_impact':
                # 成本影响排序比较复杂，先按异常数量排序
                sort_column = func.count(QualityException.id)
            elif sort_field == 'responsible_person':
                sort_column = QualityException.responsible_person
            else:
                # 默认按异常数量排序
                sort_column = func.count(QualityException.id)

            # 应用排序方向
            if sort_order.lower() == 'asc':
                query = query.order_by(sort_column.asc())
            else:
                query = query.order_by(sort_column.desc())
        else:
            # 默认排序
            query = query.order_by(func.count(QualityException.id).desc())

        # 获取所有结果
        results = query.all()
        
        # 格式化数据并计算异常成本
        data = []
        for result in results:
            dept_name, person, dept_id, matched_dept_name, count, hours, avg_hours = result

            # 处理空值
            hours = float(hours) if hours else 0
            avg_hours = float(avg_hours) if avg_hours else 0

            # 实现字段回退机制：优先使用responsible_dept_name，如果为空则使用responsible_person
            display_dept = dept_name if dept_name else person
            query_field = dept_name if dept_name else person

            # 计算该部门的异常成本
            dept_cost = 0
            if hours > 0 and query_field:
                # 获取该部门在指定时间范围内的异常记录
                if dept_name:
                    # 使用responsible_dept_name查询
                    dept_exceptions = db.session.query(QualityException).filter(
                        QualityException.responsible_dept_name == dept_name,
                        QualityException.sync_status == 1
                    )
                else:
                    # 回退到使用responsible_person查询
                    dept_exceptions = db.session.query(QualityException).filter(
                        QualityException.responsible_person == person,
                        QualityException.sync_status == 1
                    )

                # 应用时间筛选（优先使用discovery_time，如果为空则使用create_time）
                if start_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time >= start_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time >= start_date
                            )
                        )
                    )
                if end_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time <= end_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time <= end_date
                            )
                        )
                    )

                # 计算每个异常的成本
                for exception in dept_exceptions.all():
                    if exception.estimated_hours and exception.responsible_dept_id:
                        # 使用发现时间，如果为空则使用创建时间
                        discovery_time = exception.discovery_time or exception.create_time
                        if discovery_time:
                            exception_cost = DeptUnitCostService.calculate_exception_cost(
                                dept_id=exception.responsible_dept_id,
                                estimated_hours=exception.estimated_hours,
                                discovery_time=discovery_time
                            )
                            dept_cost += exception_cost

            # 最终显示的部门名称：优先使用匹配的部门名称，然后是responsible_dept_name，最后是responsible_person
            final_display_dept = matched_dept_name if matched_dept_name else display_dept

            data.append({
                'responsible_person': person,  # 保留原字段用于兼容性
                'responsible_dept_name': final_display_dept,  # 前端期望的字段名
                'dept_id': dept_id,
                'dept_name': final_display_dept,
                'exception_count': count,
                'total_hours': round(hours, 2),
                'avg_hours': round(avg_hours, 2),
                'cost_impact': round(dept_cost, 2)
            })
        
        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'data': data
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}',
            'data': []
        })


@bp.route('/api/dept_comparison')
@login_required
@authorize("system:exception_cost:view")
def api_dept_comparison():
    """部门成本对比分析API"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 如果没有提供时间范围，默认使用当年数据
        if not start_date and not end_date:
            start_date, end_date = get_current_year_range()

        # 获取排序参数
        sort_field = request.args.get('sort_field', '')
        sort_order = request.args.get('sort_order', 'desc')  # asc 或 desc

        # 验证排序参数
        valid_sort_fields = ['dept_name', 'responsible_count', 'responsible_cost', 'originator_count', 'originator_cost', 'cost_gap', 'cost_gap_ratio']
        valid_sort_orders = ['asc', 'desc']

        if sort_field and sort_field not in valid_sort_fields:
            sort_field = ''  # 重置为默认
        if sort_order and sort_order.lower() not in valid_sort_orders:
            sort_order = 'desc'  # 重置为默认

        # 1. 查询责任部门统计数据
        responsible_query = db.session.query(
            QualityException.responsible_dept_id.label('dept_id'),
            Dept.dept_name,
            func.count(QualityException.id).label('exception_count'),
            func.sum(QualityException.estimated_hours).label('total_hours'),
            func.avg(QualityException.estimated_hours).label('avg_hours')
        ).outerjoin(
            Dept, QualityException.responsible_dept_id == Dept.id
        ).filter(
            QualityException.sync_status == 1,
            QualityException.responsible_dept_id.isnot(None)
        )

        # 2. 查询发起部门统计数据
        originator_query = db.session.query(
            QualityException.originator_local_dept_id.label('dept_id'),
            Dept.dept_name,
            func.count(QualityException.id).label('exception_count'),
            func.sum(QualityException.estimated_hours).label('total_hours'),
            func.avg(QualityException.estimated_hours).label('avg_hours')
        ).outerjoin(
            Dept, QualityException.originator_local_dept_id == Dept.id
        ).filter(
            QualityException.sync_status == 1,
            QualityException.originator_local_dept_id.isnot(None)
        )

        # 应用时间筛选
        if start_date:
            responsible_query = responsible_query.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )
            originator_query = originator_query.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )

        if end_date:
            responsible_query = responsible_query.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )
            originator_query = originator_query.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )

        # 部门筛选
        if dept_id:
            responsible_query = responsible_query.filter(QualityException.responsible_dept_id == dept_id)
            originator_query = originator_query.filter(QualityException.originator_local_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        # 执行查询
        responsible_results = responsible_query.group_by(
            QualityException.responsible_dept_id, Dept.dept_name
        ).all()

        originator_results = originator_query.group_by(
            QualityException.originator_local_dept_id, Dept.dept_name
        ).all()

        # 计算成本并整合数据
        dept_comparison = {}

        # 处理责任部门数据
        for result in responsible_results:
            dept_id, dept_name, count, hours, avg_hours = result
            if not dept_id:
                continue

            hours = float(hours) if hours else 0

            # 计算责任成本
            responsible_cost = 0
            if hours > 0:
                dept_exceptions = db.session.query(QualityException).filter(
                    QualityException.responsible_dept_id == dept_id,
                    QualityException.sync_status == 1
                )

                # 应用时间筛选
                if start_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time >= start_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time >= start_date
                            )
                        )
                    )
                if end_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time <= end_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time <= end_date
                            )
                        )
                    )

                for exception in dept_exceptions.all():
                    if exception.estimated_hours and exception.responsible_dept_id:
                        discovery_time = exception.discovery_time or exception.create_time
                        if discovery_time:
                            exception_cost = DeptUnitCostService.calculate_exception_cost(
                                dept_id=exception.responsible_dept_id,
                                estimated_hours=exception.estimated_hours,
                                discovery_time=discovery_time
                            )
                            responsible_cost += exception_cost

            dept_comparison[dept_id] = {
                'dept_id': dept_id,
                'dept_name': dept_name or '未分配部门',
                'responsible_count': count,
                'responsible_hours': round(hours, 2),
                'responsible_cost': round(responsible_cost, 2),
                'originator_count': 0,
                'originator_hours': 0,
                'originator_cost': 0
            }

        # 处理发起部门数据
        for result in originator_results:
            dept_id, dept_name, count, hours, avg_hours = result
            if not dept_id:
                continue

            hours = float(hours) if hours else 0

            # 计算发起成本（使用责任部门的工时成本）
            originator_cost = 0
            if hours > 0:
                dept_exceptions = db.session.query(QualityException).filter(
                    QualityException.originator_local_dept_id == dept_id,
                    QualityException.sync_status == 1
                )

                # 应用时间筛选
                if start_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time >= start_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time >= start_date
                            )
                        )
                    )
                if end_date:
                    dept_exceptions = dept_exceptions.filter(
                        or_(
                            QualityException.discovery_time <= end_date,
                            and_(
                                QualityException.discovery_time.is_(None),
                                QualityException.create_time <= end_date
                            )
                        )
                    )

                for exception in dept_exceptions.all():
                    if exception.estimated_hours and exception.responsible_dept_id:
                        discovery_time = exception.discovery_time or exception.create_time
                        if discovery_time:
                            exception_cost = DeptUnitCostService.calculate_exception_cost(
                                dept_id=exception.responsible_dept_id,
                                estimated_hours=exception.estimated_hours,
                                discovery_time=discovery_time
                            )
                            originator_cost += exception_cost

            if dept_id not in dept_comparison:
                dept_comparison[dept_id] = {
                    'dept_id': dept_id,
                    'dept_name': dept_name or '未分配部门',
                    'responsible_count': 0,
                    'responsible_hours': 0,
                    'responsible_cost': 0,
                    'originator_count': count,
                    'originator_hours': round(hours, 2),
                    'originator_cost': round(originator_cost, 2)
                }
            else:
                dept_comparison[dept_id]['originator_count'] = count
                dept_comparison[dept_id]['originator_hours'] = round(hours, 2)
                dept_comparison[dept_id]['originator_cost'] = round(originator_cost, 2)

        # 计算差距指数并格式化最终数据
        comparison_data = []
        for dept_data in dept_comparison.values():
            cost_gap = dept_data['responsible_cost'] - dept_data['originator_cost']
            dept_data['cost_gap'] = round(cost_gap, 2)
            dept_data['cost_gap_ratio'] = round(
                (cost_gap / max(dept_data['responsible_cost'], dept_data['originator_cost'], 1)) * 100, 1
            )
            comparison_data.append(dept_data)

        # 根据排序参数进行排序
        if sort_field and sort_order:
            reverse_order = sort_order.lower() == 'desc'

            if sort_field == 'dept_name':
                comparison_data.sort(key=lambda x: x['dept_name'] or '', reverse=reverse_order)
            elif sort_field == 'responsible_count':
                comparison_data.sort(key=lambda x: x['responsible_count'], reverse=reverse_order)
            elif sort_field == 'responsible_cost':
                comparison_data.sort(key=lambda x: x['responsible_cost'], reverse=reverse_order)
            elif sort_field == 'originator_count':
                comparison_data.sort(key=lambda x: x['originator_count'], reverse=reverse_order)
            elif sort_field == 'originator_cost':
                comparison_data.sort(key=lambda x: x['originator_cost'], reverse=reverse_order)
            elif sort_field == 'cost_gap':
                comparison_data.sort(key=lambda x: x['cost_gap'], reverse=reverse_order)
            elif sort_field == 'cost_gap_ratio':
                comparison_data.sort(key=lambda x: x['cost_gap_ratio'], reverse=reverse_order)
            else:
                # 默认按成本差距绝对值排序
                comparison_data.sort(key=lambda x: abs(x['cost_gap']), reverse=True)
        else:
            # 默认按成本差距绝对值排序
            comparison_data.sort(key=lambda x: abs(x['cost_gap']), reverse=True)

        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'data': comparison_data
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}',
            'data': []
        })


@bp.route('/api/chart_data')
@login_required
@authorize("system:exception_cost:view")
def api_chart_data():
    """图表数据API"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 如果没有提供时间范围，默认使用当年数据
        if not start_date and not end_date:
            start_date, end_date = get_current_year_range()

        # 构建基础查询
        base_query = QualityException.query.filter(QualityException.sync_status == 1)

        # 时间范围筛选（优先使用discovery_time，如果为空则使用create_time）
        if start_date:
            base_query = base_query.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )
        if end_date:
            base_query = base_query.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )

        # 部门筛选
        if dept_id:
            base_query = base_query.filter(QualityException.responsible_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        # 1. 部门异常分布饼图数据 - 通过responsible_person字段统计
        dept_distribution = db.session.query(
            QualityException.responsible_person,
            func.count(QualityException.id).label('count')
        ).filter(
            QualityException.sync_status == 1,
            QualityException.responsible_person.isnot(None),
            QualityException.responsible_person != ''
        )

        # 应用筛选条件（优先使用discovery_time，如果为空则使用create_time）
        if start_date:
            dept_distribution = dept_distribution.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )
        if end_date:
            dept_distribution = dept_distribution.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )
        if dept_id:
            dept_distribution = dept_distribution.filter(QualityException.responsible_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        dept_results = dept_distribution.group_by(QualityException.responsible_person).all()
        pie_data = [{'name': dept_name or '未分配', 'value': count} for dept_name, count in dept_results if count > 0]

        # 2. 月度异常趋势数据（使用create_time，因为discovery_time大多为空）
        monthly_trend = db.session.query(
            extract('year', QualityException.create_time).label('year'),
            extract('month', QualityException.create_time).label('month'),
            func.count(QualityException.id).label('count'),
            func.sum(QualityException.estimated_hours).label('hours')
        ).filter(
            QualityException.sync_status == 1
        )

        # 应用筛选条件（使用create_time）
        if start_date:
            monthly_trend = monthly_trend.filter(QualityException.create_time >= start_date)
        if end_date:
            monthly_trend = monthly_trend.filter(QualityException.create_time <= end_date)
        if dept_id:
            monthly_trend = monthly_trend.filter(QualityException.responsible_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        monthly_results = monthly_trend.group_by(
            extract('year', QualityException.create_time),
            extract('month', QualityException.create_time)
        ).order_by('year', 'month').all()

        trend_data = {
            'months': [],
            'counts': [],
            'hours': []
        }

        for year, month, count, hours in monthly_results:
            if year and month:
                month_str = f"{int(year)}-{int(month):02d}"
                trend_data['months'].append(month_str)
                trend_data['counts'].append(count)
                trend_data['hours'].append(float(hours) if hours else 0)

        # 3. 月度成本影响趋势数据（使用真实部门时薪计算）
        # 首先查询异常数据，包含部门和工时信息
        cost_trend_query = db.session.query(
            extract('year', QualityException.create_time).label('year'),
            extract('month', QualityException.create_time).label('month'),
            QualityException.responsible_dept_id,
            QualityException.estimated_hours,
            QualityException.cost_impact
        ).filter(
            QualityException.sync_status == 1
        )

        # 应用筛选条件（优先使用discovery_time，如果为空则使用create_time）
        if start_date:
            cost_trend_query = cost_trend_query.filter(
                or_(
                    QualityException.discovery_time >= start_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time >= start_date
                    )
                )
            )
        if end_date:
            cost_trend_query = cost_trend_query.filter(
                or_(
                    QualityException.discovery_time <= end_date,
                    and_(
                        QualityException.discovery_time.is_(None),
                        QualityException.create_time <= end_date
                    )
                )
            )
        if dept_id:
            cost_trend_query = cost_trend_query.filter(QualityException.responsible_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        # 执行查询获取所有异常记录
        cost_trend_results = cost_trend_query.all()

        # 按月份分组处理数据
        monthly_data = {}
        for year, month, dept_id, estimated_hours, cost_impact in cost_trend_results:
            if year and month:
                month_str = f"{int(year)}-{int(month):02d}"

                if month_str not in monthly_data:
                    monthly_data[month_str] = {
                        'exception_count': 0,
                        'total_cost_db': 0.0,  # 数据库中的成本
                        'total_cost_calculated': 0.0,  # 基于时薪计算的成本
                        'total_hours': 0.0
                    }

                monthly_data[month_str]['exception_count'] += 1
                monthly_data[month_str]['total_cost_db'] += float(cost_impact) if cost_impact else 0.0
                monthly_data[month_str]['total_hours'] += float(estimated_hours) if estimated_hours else 0.0

                # 使用真实部门时薪计算成本
                if dept_id and estimated_hours and estimated_hours > 0:
                    unit_cost = DeptMonthlyUnitCost.get_unit_cost(dept_id, month_str)
                    if unit_cost > 0:
                        calculated_cost = float(estimated_hours) * unit_cost
                        monthly_data[month_str]['total_cost_calculated'] += calculated_cost

        # 转换为列表格式并排序
        cost_trend_data = []
        for month_str in sorted(monthly_data.keys()):
            data = monthly_data[month_str]

            # 优先使用基于时薪计算的成本，如果没有则使用数据库中的成本
            final_cost = data['total_cost_calculated'] if data['total_cost_calculated'] > 0 else data['total_cost_db']

            # 如果都没有成本数据，使用默认估算（1500元/异常）
            if final_cost == 0 and data['exception_count'] > 0:
                final_cost = data['exception_count'] * 1500

            cost_trend_data.append({
                'month': month_str,
                'exception_count': data['exception_count'],
                'total_cost': round(final_cost, 2),
                'total_hours': round(data['total_hours'], 2),
                'cost_source': 'calculated' if data['total_cost_calculated'] > 0 else ('database' if data['total_cost_db'] > 0 else 'estimated')
            })

        return jsonify({
            'code': 0,
            'msg': '查询成功',
            'data': {
                'pie_data': pie_data,
                'trend_data': trend_data,
                'cost_trend_data': cost_trend_data
            }
        })

    except Exception as e:
        return jsonify({
            'code': 500,
            'msg': f'查询失败: {str(e)}',
            'data': {
                'pie_data': [],
                'trend_data': {'months': [], 'counts': [], 'hours': []},
                'cost_trend_data': []
            }
        })


@bp.route('/export/dept_statistics')
@login_required
@authorize("system:exception_cost:export")
def export_dept_statistics():
    """导出部门异常统计Excel"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 构建查询条件（复用API逻辑）
        query = db.session.query(
            QualityException.responsible_dept_id,
            Dept.dept_name,
            func.count(QualityException.id).label('exception_count'),
            func.sum(QualityException.estimated_hours).label('total_hours'),
            func.avg(QualityException.estimated_hours).label('avg_hours'),
            func.sum(QualityException.cost_impact).label('total_cost_impact')
        ).outerjoin(
            Dept, QualityException.responsible_dept_id == Dept.id
        ).filter(
            QualityException.sync_status == 1
        )

        # 应用筛选条件
        if start_date:
            query = query.filter(QualityException.discovery_time >= start_date)
        if end_date:
            query = query.filter(QualityException.discovery_time <= end_date)
        if dept_id:
            query = query.filter(QualityException.responsible_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        results = query.group_by(
            QualityException.responsible_dept_id, Dept.dept_name
        ).order_by(
            func.count(QualityException.id).desc()
        ).all()

        # 准备Excel数据
        excel_data = []
        for result in results:
            dept_id, dept_name, count, hours, avg_hours, cost_impact = result
            excel_data.append({
                '部门名称': dept_name or '未分配部门',
                '异常数量': count,
                '总预估工时(小时)': round(float(hours) if hours else 0, 2),
                '平均预估工时(小时)': round(float(avg_hours) if avg_hours else 0, 2),
                '总成本影响': round(float(cost_impact) if cost_impact else 0, 2)
            })

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='部门异常统计', index=False)

        output.seek(0)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'部门异常统计_{timestamp}.xlsx'

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        return fail_api(msg=f'导出失败: {str(e)}')


@bp.route('/export/dept_comparison')
@login_required
@authorize("system:exception_cost:export")
def export_dept_comparison():
    """导出部门成本对比分析Excel"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 创建示例数据（实际应用中应该调用对比数据逻辑）
        excel_data = [
            {
                '部门名称': '技术部',
                '责任异常数': 15,
                '责任工时': 120.5,
                '责任成本': 8500.00,
                '发起异常数': 8,
                '发起工时': 64.0,
                '发起成本': 4200.00,
                '成本差距': 4300.00,
                '差距比例(%)': 51.2
            },
            {
                '部门名称': '生产部',
                '责任异常数': 22,
                '责任工时': 180.0,
                '责任成本': 12000.00,
                '发起异常数': 18,
                '发起工时': 144.0,
                '发起成本': 9600.00,
                '成本差距': 2400.00,
                '差距比例(%)': 20.0
            },
            {
                '部门名称': '质量部',
                '责任异常数': 5,
                '责任工时': 40.0,
                '责任成本': 2800.00,
                '发起异常数': 12,
                '发起工时': 96.0,
                '发起成本': 6720.00,
                '成本差距': -3920.00,
                '差距比例(%)': -58.3
            }
        ]

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='部门成本对比分析', index=False)

            # 获取工作表进行格式化
            worksheet = writer.sheets['部门成本对比分析']

            # 设置列宽
            column_widths = [15, 12, 12, 12, 12, 12, 12, 12, 12]
            for i, width in enumerate(column_widths):
                worksheet.column_dimensions[chr(65 + i)].width = width

        output.seek(0)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'部门成本对比分析_{timestamp}.xlsx'

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        return fail_api(msg=f'导出失败: {str(e)}')


@bp.route('/export/person_statistics')
@login_required
@authorize("system:exception_cost:export")
def export_person_statistics():
    """导出责任部门异常统计Excel"""
    try:
        # 获取筛选参数
        start_date = request.args.get('start_date', '')
        end_date = request.args.get('end_date', '')
        dept_id = request.args.get('dept_id', '')

        # 构建查询条件（复用API逻辑）- 使用responsible_dept_name字段
        query = db.session.query(
            QualityException.responsible_dept_name,
            QualityException.responsible_person,
            QualityException.responsible_dept_id,
            Dept.dept_name.label('matched_dept_name'),
            func.count(QualityException.id).label('exception_count'),
            func.sum(QualityException.estimated_hours).label('total_hours'),
            func.avg(QualityException.estimated_hours).label('avg_hours'),
            func.sum(QualityException.cost_impact).label('total_cost_impact')
        ).outerjoin(
            Dept, QualityException.responsible_dept_name == Dept.dept_name
        ).filter(
            QualityException.sync_status == 1,
            or_(
                and_(
                    QualityException.responsible_dept_name.isnot(None),
                    QualityException.responsible_dept_name != ''
                ),
                and_(
                    QualityException.responsible_dept_name.is_(None),
                    QualityException.responsible_person.isnot(None),
                    QualityException.responsible_person != ''
                )
            )
        )

        # 应用筛选条件
        if start_date:
            query = query.filter(QualityException.discovery_time >= start_date)
        if end_date:
            query = query.filter(QualityException.discovery_time <= end_date)
        if dept_id:
            query = query.filter(QualityException.responsible_dept_id == dept_id)

        # 移除用户权限筛选，允许查看所有部门数据

        results = query.group_by(
            QualityException.responsible_dept_name,
            QualityException.responsible_person,
            QualityException.responsible_dept_id,
            Dept.dept_name
        ).order_by(
            func.count(QualityException.id).desc()
        ).all()

        # 准备Excel数据
        excel_data = []
        for result in results:
            dept_name, person, dept_id, matched_dept_name, count, hours, avg_hours, cost_impact = result

            # 实现字段回退机制：优先使用responsible_dept_name，如果为空则使用responsible_person
            display_dept = dept_name if dept_name else person
            # 最终显示的部门名称：优先使用匹配的部门名称，然后是responsible_dept_name，最后是responsible_person
            final_display_dept = matched_dept_name if matched_dept_name else display_dept

            excel_data.append({
                '部门名称': final_display_dept,
                '异常数量': count,
                '总预估工时(小时)': round(float(hours) if hours else 0, 2),
                '平均预估工时(小时)': round(float(avg_hours) if avg_hours else 0, 2),
                '总成本影响': round(float(cost_impact) if cost_impact else 0, 2)
            })

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 创建Excel文件
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='部门异常统计', index=False)

        output.seek(0)

        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f'部门异常统计_{timestamp}.xlsx'

        return send_file(
            output,
            mimetype='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            as_attachment=True,
            download_name=filename
        )

    except Exception as e:
        return fail_api(msg=f'导出失败: {str(e)}')


# ==================== 部门工时单位成本相关API ====================

@bp.route('/api/unit_cost_config')
@login_required
@authorize("system:exception_cost:unit_cost_config")
def api_get_unit_cost_config():
    """获取部门工时单位成本定时任务配置API"""
    try:
        from flask import session

        config = DeptUnitCostConfigService.get_config_for_frontend()

        # 检查用户权限
        user_permissions = session.get('permissions', [])
        permissions = {
            'config': 'system:exception_cost:unit_cost_config' in user_permissions,
            'trigger': 'system:exception_cost:unit_cost_trigger' in user_permissions
        }

        # 如果是超级管理员，拥有所有权限
        if current_user.username == current_app.config.get("SUPERADMIN"):
            permissions = {'config': True, 'trigger': True}

        # 将权限信息添加到配置中
        config['permissions'] = permissions

        return success_api(data=config, msg='获取配置成功')
    except Exception as e:
        return fail_api(msg=f'获取配置失败: {str(e)}')


@bp.route('/api/unit_cost_config', methods=['POST'])
@login_required
@authorize("system:exception_cost:unit_cost_config")
def api_update_unit_cost_config():
    """更新部门工时单位成本定时任务配置API"""
    try:
        data = request.get_json()
        if not data:
            return fail_api(msg='请求数据不能为空')

        # 构建配置更新字典
        config_updates = {}

        # 验证并设置启用状态
        if 'enabled' in data:
            enabled = '1' if data['enabled'] else '0'
            config_updates['enabled'] = enabled

        # 验证并设置执行频率
        if 'frequency' in data:
            frequency = data['frequency']
            if frequency not in ['daily', 'weekly', 'monthly']:
                return fail_api(msg='执行频率值无效')
            config_updates['frequency'] = frequency

        # 验证并设置执行时间
        if 'hour' in data:
            try:
                hour = int(data['hour'])
                if not (0 <= hour <= 23):
                    return fail_api(msg='执行小时必须在0-23之间')
                config_updates['hour'] = str(hour)
            except (ValueError, TypeError):
                return fail_api(msg='执行小时格式无效')

        if 'minute' in data:
            try:
                minute = int(data['minute'])
                if not (0 <= minute <= 59):
                    return fail_api(msg='执行分钟必须在0-59之间')
                config_updates['minute'] = str(minute)
            except (ValueError, TypeError):
                return fail_api(msg='执行分钟格式无效')

        # 验证并设置每月执行日期
        if 'day_of_month' in data:
            try:
                day_of_month = int(data['day_of_month'])
                if not (1 <= day_of_month <= 31):
                    return fail_api(msg='每月执行日期必须在1-31之间')
                config_updates['day_of_month'] = str(day_of_month)
            except (ValueError, TypeError):
                return fail_api(msg='每月执行日期格式无效')

        # 验证并设置每周执行日期
        if 'day_of_week' in data:
            try:
                day_of_week = int(data['day_of_week'])
                if not (0 <= day_of_week <= 6):
                    return fail_api(msg='每周执行日期必须在0-6之间')
                config_updates['day_of_week'] = str(day_of_week)
            except (ValueError, TypeError):
                return fail_api(msg='每周执行日期格式无效')

        # 验证并设置计算月份跨度
        if 'months_span' in data:
            try:
                months_span = int(data['months_span'])
                if not (1 <= months_span <= 12):
                    return fail_api(msg='计算月份跨度必须在1-12之间')
                config_updates['months_span'] = str(months_span)
            except (ValueError, TypeError):
                return fail_api(msg='计算月份跨度格式无效')

        if not config_updates:
            return fail_api(msg='没有有效的配置更新')

        # 批量更新配置
        success = DeptUnitCostConfigService.batch_update_config(config_updates)
        if not success:
            return fail_api(msg='配置更新失败')

        # 更新定时任务
        from applications.extensions.init_scheduler import update_dept_unit_cost_calculation_job
        update_success = update_dept_unit_cost_calculation_job()

        if update_success:
            return success_api(msg='配置更新成功，定时任务已重新调度')
        else:
            return success_api(msg='配置更新成功，但定时任务更新失败，请检查日志')

    except Exception as e:
        return fail_api(msg=f'配置更新失败: {str(e)}')


@bp.route('/api/manual_calculate', methods=['POST'])
@login_required
@authorize("system:exception_cost:unit_cost_trigger")
def api_manual_calculate_unit_cost():
    """手动触发部门工时单位成本计算API"""
    try:
        from applications.extensions.init_scheduler import manual_trigger_dept_unit_cost_calculation

        result = manual_trigger_dept_unit_cost_calculation()

        if result['success']:
            return success_api(data=result, msg='计算任务执行成功')
        else:
            return fail_api(msg=f'计算任务执行失败: {result.get("error", "未知错误")}')

    except Exception as e:
        return fail_api(msg=f'触发计算失败: {str(e)}')


@bp.route('/api/unit_cost_data')
@login_required
@authorize("system:exception_cost:view")
def api_get_unit_cost_data():
    """获取部门工时单位成本数据API"""
    try:
        # 获取筛选参数
        dept_id = request.args.get('dept_id', '')
        year_month = request.args.get('year_month', '')
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))

        # 构建查询
        query = DeptMonthlyUnitCost.query.join(
            Dept, DeptMonthlyUnitCost.dept_id == Dept.id
        )

        # 部门筛选
        if dept_id:
            query = query.filter(DeptMonthlyUnitCost.dept_id == dept_id)

        # 年月筛选
        if year_month:
            query = query.filter(DeptMonthlyUnitCost.year_month == year_month)

        # 移除用户权限筛选，允许查看所有部门数据

        # 排序和分页
        query = query.order_by(
            DeptMonthlyUnitCost.year_month.desc(),
            Dept.dept_name.asc()
        )

        # 分页
        total = query.count()
        items = query.offset((page - 1) * limit).limit(limit).all()

        # 转换数据格式
        data = [item.to_dict() for item in items]

        return table_api(data=data, count=total)

    except Exception as e:
        return fail_api(msg=f'查询失败: {str(e)}')
