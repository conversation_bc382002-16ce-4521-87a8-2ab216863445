<!DOCTYPE html>
<html>
<head>
    <title>质量异常单管理</title>
    {% include 'system/common/header.html' %}
    <style>
        .search-form {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .search-form .layui-form-item {
            margin-bottom: 0;
            display: flex;
            align-items: center;
            height: 38px;
        }
        .search-form .layui-form-label {
            width: 80px;
            height: 38px;
            line-height: 38px;
            padding: 0 10px;
            font-weight: 500;
            color: #333;
            flex-shrink: 0;
        }
        .search-form .layui-input-block {
            margin-left: 90px;
            flex: 1;
            display: flex;
            align-items: center;
        }
        .search-form .layui-input {
            height: 38px;
            line-height: 38px;
            border-radius: 4px;
            border: 1px solid #e6e6e6;
            transition: border-color 0.3s;
            width: 100%;
        }
        .search-form .layui-input:focus {
            border-color: #009688;
        }
        .search-form .btn-group {
            display: flex;
            align-items: center;
            gap: 10px;
            height: 38px;
        }
        .search-form .btn-group .layui-btn {
            height: 38px;
            line-height: 38px;
            padding: 0 15px;
            margin: 0;
        }
        .table-container {
            background: #fff;
            padding: 15px;
            border-radius: 2px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
        }
        .page-header {
            background: #fff;
            padding: 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
        }
        .page-header h2 {
            margin: 0;
            color: #333;
            font-size: 18px;
            font-weight: 500;
        }
        .operation-search-container {
            background: #fff;
            padding: 15px 20px;
            margin-bottom: 15px;
            border-radius: 6px;
            box-shadow: 0 2px 8px 0 rgba(0,0,0,.08);
            border: 1px solid #f0f0f0;
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        .operation-buttons-left {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-shrink: 0;
        }
        .operation-buttons-left .layui-btn {
            margin: 0;
            height: 38px;
            line-height: 38px;
        }
        .search-form-right {
            flex: 1;
            min-width: 0;
        }
        .search-form-right .layui-form {
            margin: 0;
        }
        .search-form-right .layui-row {
            margin: 0;
        }
        .status-tag {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            color: #fff;
            display: inline-block;
        }
        .status-new { background-color: #1890ff; }
        .status-running { background-color: #faad14; }
        .status-completed { background-color: #52c41a; }
        .status-terminated { background-color: #f5222d; }
        .status-canceled { background-color: #d9d9d9; color: #666; }
               
       
        /* 表格响应式样式 */
        .table-container {
            overflow-x: auto;
            min-width: 1000px;
        }
        .layui-table {
            min-width: 100%;
        }
        
        /* 移动端适配 */
        @media (max-width: 1200px) {
            .table-container {
                min-width: 900px;
            }
        }
        @media (max-width: 768px) {
            .table-container {
                min-width: 700px;
            }
            .operation-search-container {
                flex-direction: column;
                align-items: stretch;
                gap: 15px;
            }
            .operation-buttons-left {
                justify-content: center;
                flex-wrap: wrap;
            }
            .search-form-right .layui-col-md3 {
                width: 50% !important;
                margin-bottom: 15px;
            }
            .search-form-right .btn-group .layui-btn {
                padding: 0 10px;
                font-size: 12px;
            }
        }
        @media (max-width: 480px) {
            .operation-buttons-left .layui-btn {
                padding: 0 10px;
                font-size: 12px;
            }
            .search-form-right .layui-col-md3 {
                width: 100% !important;
            }
            .table-container {
                min-width: 600px;
            }
        }

        /* Excel导入数据预览表格样式优化 */
        #preview-table {
            margin: 0;
            border-collapse: collapse;
            width: 100%;
            min-width: 600px;
            table-layout: auto;
        }

        #preview-table th,
        #preview-table td {
            padding: 4px 8px !important;
            height: auto !important;
            line-height: 1.2 !important;
            vertical-align: middle !important;
            border: 1px solid #e6e6e6 !important;
            font-size: 12px;
            word-break: break-all;
            word-wrap: break-word;
        }

        #preview-table th {
            background-color: #f8f9fa !important;
            font-weight: 600;
            color: #333;
            text-align: center;
            white-space: nowrap;
            height: 26px !important;
        }

        #preview-table td {
            background-color: #fff !important;
            min-width: 80px;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 22px !important;
            white-space: nowrap;
        }

        #preview-table tbody tr:nth-child(even) td {
            background-color: #fafafa !important;
        }

        #preview-table tbody tr:hover td {
            background-color: #f0f8ff !important;
        }

        /* 数据预览容器样式 */
        .data-preview-container {
            max-height: 200px;
            overflow: auto;
            border: 1px solid #e6e6e6;
            border-radius: 4px;
            background: #fff;
            width: 100%;
            min-width: 600px;
        }

        .data-preview-container::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .data-preview-container::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .data-preview-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .data-preview-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }


    </style>
</head>

<body class="pear-container">
    <!-- 页面标题 -->
    <div class="page-header">
        <h2><i class="layui-icon layui-icon-template-1" style="color: #009688;"></i> 质量异常单管理</h2>
    </div>

    <!-- 操作按钮和搜索区域 -->
    <div class="operation-search-container">
        <!-- 左侧操作按钮 -->
        <div class="operation-buttons-left">
            <!-- 钉钉同步相关按钮 -->
            {% if authorize("system:dingtalk:quality_exceptions:sync") %}
            <button type="button" class="layui-btn layui-btn-primary" id="auto-sync">
                <i class="layui-icon layui-icon-download-circle"></i> 智能同步
            </button>
            <button type="button" class="layui-btn layui-btn-normal" id="manual-sync">
                <i class="layui-icon layui-icon-refresh-3"></i> 手动同步
            </button>
            {% endif %}

            <!-- Excel导入相关按钮 -->
            {% if authorize("system:dingtalk:quality_exceptions:import") %}
            <button type="button" class="layui-btn layui-btn-warm" id="excel-import">
                <i class="layui-icon layui-icon-upload"></i> Excel导入
            </button>
            {% endif %}

            {% if authorize("system:dingtalk:quality_exceptions:template") %}
            <button type="button" class="layui-btn layui-btn-primary" id="download-template">
                <i class="layui-icon layui-icon-download-circle"></i> 下载模板
            </button>
            {% endif %}

            <!-- 其他功能按钮 -->
            {% if authorize("system:dingtalk:quality_exceptions:view") %}
            <button type="button" class="layui-btn layui-btn-warm" id="refresh-status">
                <i class="layui-icon layui-icon-refresh"></i> 刷新状态
            </button>
            {% endif %}
        </div>

        <!-- 右侧搜索表单 -->
        <div class="search-form-right">
            <form class="layui-form" lay-filter="search-form">
                <div class="layui-row layui-col-space15">
                    <div class="layui-col-md3">
                        <div class="layui-form-item" style="margin-bottom: 0px;">
                            <label class="layui-form-label">审批状态</label>
                            <div class="layui-input-block" >
                                <select name="status" id="status-filter" lay-search>
                                    <option value="">全部状态</option>
                                    <!-- <option value="NEW">新建</option>
                                    <option value="RUNNING">审批中</option> -->
                                    <option value="COMPLETED">已完成</option>
                                    <!-- <option value="TERMINATED">已终止</option>
                                    <option value="CANCELED">已取消</option> -->
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item" style="margin-bottom: 0px;">
                            <label class="layui-form-label">项目类型</label>
                            <div class="layui-input-block" >
                                <input type="text" name="project_type" id="project-type-filter" placeholder="请输入项目类型" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item" style="margin-bottom: 0px;">
                            <label class="layui-form-label">项目编号</label>
                            <div class="layui-input-block" >
                                <input type="text" name="project_code" id="project-code-filter" placeholder="请输入项目编号" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-col-md3">
                        <div class="layui-form-item" style="margin-bottom: 0px;">
                            <label class="layui-form-label" style="width: 0; padding: 0; margin: 0;"></label>
                            <div class="layui-input-block btn-group" style="margin-left: 0;">
                                <button type="button" class="layui-btn layui-btn-primary" id="search-btn">
                                    <i class="layui-icon layui-icon-search"></i> 搜索
                                </button>
                                <button type="button" class="layui-btn layui-btn-danger" id="reset-btn">
                                    <i class="layui-icon layui-icon-refresh-1"></i> 重置
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-container">
        <table class="layui-hide" id="quality-exceptions-table" lay-filter="quality-exceptions-table"></table>
    </div>

    <!-- 工具栏模板 -->
    <script type="text/html" id="toolbar-tpl">
        <a class="layui-btn layui-btn-xs layui-btn-normal" lay-event="detail">
            <i class="layui-icon layui-icon-about"></i> 详情
        </a>
    </script>

    <!-- 状态标签模板 -->
    <script type="text/html" id="status-tpl">
        {% raw %}
        {{# if(d.approval_status === 'NEW'){ }}
            <span class="status-tag status-new">新建</span>
        {{# } else if(d.approval_status === 'RUNNING'){ }}
            <span class="status-tag status-running">审批中</span>
        {{# } else if(d.approval_status === 'COMPLETED'){ }}
            <span class="status-tag status-completed">已完成</span>
        {{# } else if(d.approval_status === 'TERMINATED'){ }}
            <span class="status-tag status-terminated">已终止</span>
        {{# } else if(d.approval_status === 'CANCELED'){ }}
            <span class="status-tag status-canceled">已取消</span>
        {{# } else { }}
            <span class="status-tag status-canceled">{{d.approval_status || '未知'}}</span>
        {{# } }}
        {% endraw %}
    </script>



    {% include 'system/common/footer.html' %}

    <script>
        layui.use(['table', 'form', 'laydate', 'layer', 'jquery', 'upload'], function(){
            var table = layui.table;
            var form = layui.form;
            var laydate = layui.laydate;
            var layer = layui.layer;
            var upload = layui.upload;
            var $ = layui.jquery;

            // 设置质量异常单的process_code
            window.qualityExceptionProcessCode = 'PROC-CDDAA486-7E7E-4AB0-86F5-F7CF22B9E5BE';

            // 确保表单正确渲染
            form.render();

            // 初始化表格
            var tableIns = table.render({
                elem: '#quality-exceptions-table',
                url: '/system/dindin/api/quality-exceptions/list',
                method: 'GET',
                page: true,
                limit: 20,
                limits: [10, 20, 50, 100],
                cols: [[
                    {field: 'id', title: 'ID', width: 80, sort: true},
                    {field: 'title', title: '标题', width: 200, templet: function(d){
                        return '<div style="word-break: break-all; white-space: normal;">' + (d.title || '-') + '</div>';
                    }},
                    {field: 'project_type', title: '项目类型', width: 80},
                    {field: 'project_code', title: '项目编号', width: 120},
                    {field: 'machine_number', title: '机台编号', width: 120},
                    {field: 'exception_quantity', title: '异常数量', width: 100, align: 'center'},
                    {field: 'estimated_hours', title: '预估工时', width: 100, align: 'center'},
                    {field: 'originator_dept_name', title: '发起部门', width: 120},
                    {field: 'responsible_dept_name', title: '责任部门', width: 120},
                    {field: 'responsible_person', title: '责任人', width: 100},
                    {field: 'approval_status', title: '状态', width: 100, align: 'center', templet: '#status-tpl'},
                    {field: 'create_time', title: '创建时间', width: 160, sort: true},
                    {title: '操作', width: 120, align: 'center', toolbar: '#toolbar-tpl', fixed: 'right'}
                ]],
                parseData: function(res){
                    return {
                        "code": res.code === 0 ? 0 : 1,
                        "msg": res.msg,
                        "count": res.count,
                        "data": res.data
                    };
                },
                request: {
                    pageName: 'page',
                    limitName: 'limit'
                },
                where: {},
                text: {
                    none: '暂无相关数据'
                },
                done: function(res, curr, count) {
                    // 表格渲染完成后的回调，确保表单元素正确渲染
                    form.render();
                }
            });

            // 监听工具条
            table.on('tool(quality-exceptions-table)', function(obj){
                var data = obj.data;
                var layEvent = obj.event;

                if(layEvent === 'detail'){
                    showExceptionDetail(data);
                }
            });

            // 搜索功能
            $('#search-btn').on('click', function(){
                var status = $('#status-filter').val();
                var projectType = $('#project-type-filter').val();
                var projectCode = $('#project-code-filter').val();

                tableIns.reload({
                    where: {
                        status: status,
                        project_type: projectType,
                        project_code: projectCode
                    },
                    page: {
                        curr: 1
                    }
                });

                // 确保搜索后表单元素正确渲染
                setTimeout(function() {
                    form.render();
                }, 100);
            });

            // 重置功能
            $('#reset-btn').on('click', function(){
                $('#status-filter').val('');
                $('#project-type-filter').val('');
                $('#project-code-filter').val('');
                form.render('select');

                tableIns.reload({
                    where: {},
                    page: {
                        curr: 1
                    }
                });

                // 确保重置后表单元素正确渲染
                setTimeout(function() {
                    form.render();
                }, 100);
            });

            // 智能同步配置弹窗
            $('#auto-sync').on('click', function(){
                showAutoSyncConfig();
            });

            // 切换频率配置显示
            function toggleFrequencyConfig(frequencyType) {
                $('#weekly-config').hide();
                $('#monthly-config').hide();

                if (frequencyType === 'weekly') {
                    $('#weekly-config').show();
                } else if (frequencyType === 'monthly') {
                    $('#monthly-config').show();
                }
            }

            // 加载已保存的配置
            function loadSavedConfig() {
                $.ajax({
                    url: '/system/dindin/api/sync-config',
                    type: 'GET',
                    success: function(res) {
                        if(res.success && res.data) {
                            var config = res.data;

                            // 延迟执行，确保表单元素已渲染
                            setTimeout(function() {
                                // 填充表单数据
                                $('select[name="sync_days"]').val(config.sync_days || 7);
                                $('input[name="sync_mode"][value="' + (config.sync_mode || 'incremental') + '"]').prop('checked', true);
                                $('input[name="enable_schedule"]').prop('checked', config.enable_schedule || false);
                                $('select[name="frequency_type"]').val(config.frequency_type || 'daily');
                                $('select[name="week_day"]').val(config.week_day || 6);
                                $('select[name="month_day"]').val(config.month_day || 1);
                                $('input[name="schedule_time"]').val(config.schedule_time || '02:00');

                                // 处理状态过滤复选框
                                var syncStatus = config.sync_status || ['NEW', 'RUNNING', 'COMPLETED'];
                                $('input[name="sync_status[]"]').prop('checked', false); // 先清空所有选择
                                if (Array.isArray(syncStatus)) {
                                    syncStatus.forEach(function(status) {
                                        $('input[name="sync_status[]"][value="' + status + '"]').prop('checked', true);
                                    });
                                }

                                // 触发表单渲染更新
                                form.render();

                                // 根据配置显示相应的选项
                                if(config.enable_schedule) {
                                    $('#schedule-config').show();
                                    $('#schedule-time').show();
                                    toggleFrequencyConfig(config.frequency_type);
                                }
                            }, 100);
                        }
                    },
                    error: function() {
                        console.log('加载配置失败，使用默认配置');
                    }
                });
            }

            // 显示智能同步配置弹窗
            function showAutoSyncConfig() {
                var content = `
                    <div style="padding: 20px;">
                        <form class="layui-form" lay-filter="auto-sync-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 100px;text-align: left;">同步时间范围:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <select name="sync_days" lay-verify="required">
                                        <option value="">请选择同步天数</option>
                                        <option value="1">最近1天</option>
                                        <option value="3">最近3天</option>
                                        <option value="7" selected>最近7天</option>
                                        <option value="15">最近15天</option>
                                        <option value="40">最近40天</option>
                                        <option value="60">最近60天</option>
                                        <option value="90">最近90天</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;text-align: left;">同步模式:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <input type="radio" name="sync_mode" value="incremental" title="增量同步" checked>
                                    <input type="radio" name="sync_mode" value="full" title="全量同步">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;text-align: left;">状态过滤:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <input type="checkbox" name="sync_status[]" value="NEW" title="新建" checked>
                                    <input type="checkbox" name="sync_status[]" value="RUNNING" title="审批中" checked>
                                    <input type="checkbox" name="sync_status[]" value="COMPLETED" title="已完成" checked>
                                    <input type="checkbox" name="sync_status[]" value="TERMINATED" title="已终止">
                                    <input type="checkbox" name="sync_status[]" value="CANCELED" title="已取消">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <label class="layui-form-label" style="width: 120px;text-align: left;">启用定时同步:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <input type="checkbox" name="enable_schedule" lay-skin="switch" lay-text="开启|关闭">
                                </div>
                            </div>

                            <div class="layui-form-item" id="schedule-config" style="display: none;">
                                <label class="layui-form-label" style="width: 90px;text-align: left;">同步频率:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <select name="frequency_type" lay-filter="frequency-type">
                                        <option value="hourly">每小时</option>
                                        <option value="daily" selected>每天</option>
                                        <option value="weekly">每周</option>
                                        <option value="monthly">每月</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 每周具体时间选择 -->
                            <div class="layui-form-item" id="weekly-config" style="display: none;">
                                <label class="layui-form-label" style="width: 90px;text-align: left;">执行日期:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <select name="week_day">
                                        <option value="0">周一</option>
                                        <option value="1">周二</option>
                                        <option value="2">周三</option>
                                        <option value="3">周四</option>
                                        <option value="4">周五</option>
                                        <option value="5">周六</option>
                                        <option value="6" selected>周日</option>
                                    </select>
                                </div>
                            </div>

                            <!-- 每月具体时间选择 -->
                            <div class="layui-form-item" id="monthly-config" style="display: none;">
                                <label class="layui-form-label" style="width: 90px;text-align: left;">执行日期:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <select name="month_day">
                                        <option value="1" selected>1号</option>
                                        <option value="2">2号</option>
                                        <option value="3">3号</option>
                                        <option value="4">4号</option>
                                        <option value="5">5号</option>
                                        <option value="10">10号</option>
                                        <option value="15">15号</option>
                                        <option value="20">20号</option>
                                        <option value="25">25号</option>
                                        <option value="last">月末</option>
                                    </select>
                                </div>
                            </div>

                            <div class="layui-form-item" id="schedule-time" style="display: none;">
                                <label class="layui-form-label" style="width: 90px;text-align: left;">执行时间:</label>
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <input type="text" name="schedule_time" placeholder="例如: 02:00" class="layui-input" style="width: 150px;">
                                </div>
                            </div>

                            <div class="layui-form-item">
                                <div class="layui-input-block" style="margin-left: 130px;">
                                    <button type="submit" class="layui-btn" lay-submit lay-filter="save-sync-config">
                                        <i class="layui-icon layui-icon-ok"></i> 保存配置
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-primary" onclick="layer.closeAll()">
                                        <i class="layui-icon layui-icon-close"></i> 取消
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '<i class="layui-icon layui-icon-set" style="margin-right: 8px;"></i>智能同步配置',
                    area: ['500px', '550px'],
                    content: content,
                    success: function(layero, index) {
                        // 重新渲染表单
                        form.render();

                        // 监听定时同步开关
                        form.on('switch()', function(data) {
                            if (data.elem.name === 'enable_schedule') {
                                if (data.elem.checked) {
                                    $('#schedule-config').show();
                                    $('#schedule-time').show();
                                    // 根据当前频率类型显示相应配置
                                    var frequencyType = $('select[name="frequency_type"]').val();
                                    toggleFrequencyConfig(frequencyType);
                                } else {
                                    $('#schedule-config').hide();
                                    $('#schedule-time').hide();
                                    $('#weekly-config').hide();
                                    $('#monthly-config').hide();
                                }
                            }
                        });

                        // 监听频率类型变化
                        form.on('select(frequency-type)', function(data) {
                            toggleFrequencyConfig(data.value);
                        });



                        // 加载已保存的配置
                        loadSavedConfig();

                        // 监听表单提交
                        form.on('submit(save-sync-config)', function(formData) {
                            saveSyncConfig(formData.field);
                            layer.close(index);
                            return false;
                        });
                    }
                });
            }

            // 保存同步配置
            function saveSyncConfig(config) {
                console.log('保存同步配置:', config);

                var loadingIndex = layer.load(1, {shade: [0.3, '#000']});

                // 构建配置参数
                // 获取选中的状态过滤
                var selectedStatuses = [];
                $('input[name="sync_status[]"]:checked').each(function() {
                    selectedStatuses.push($(this).val());
                });

                var configData = {
                    days: parseInt(config.sync_days) || 7,
                    sync_mode: config.sync_mode,
                    sync_status: selectedStatuses.length > 0 ? selectedStatuses : ['NEW', 'RUNNING', 'COMPLETED'],
                    enable_schedule: config.enable_schedule === 'on',
                    frequency_type: config.frequency_type || 'daily',
                    week_day: config.week_day,
                    month_day: config.month_day,
                    schedule_time: config.schedule_time
                };

                $.ajax({
                    url: '/system/dindin/api/sync-config/save',
                    type: 'POST',
                    contentType: 'application/json',
                    data: JSON.stringify(configData),
                    success: function(res){
                        layer.close(loadingIndex);
                        if(res.success){
                            layer.msg(res.msg, {icon: 1, time: 3000});
                        } else {
                            layer.msg(res.msg || '配置保存失败', {icon: 2});
                        }
                    },
                    error: function(xhr, status, error){
                        layer.close(loadingIndex);
                        layer.msg('请求失败: ' + error, {icon: 2, time: 3000});
                    }
                });
            }



            // 手动同步
            $('#manual-sync').on('click', function(){
                var content = `
                    <div style="padding: 20px;">
                        <form class="layui-form" lay-filter="manual-sync-form">
                            <div class="layui-form-item">
                                <label class="layui-form-label">开始日期</label>
                                <div class="layui-input-block">
                                    <input type="text" name="start_date" id="manual-start-date" placeholder="请选择开始日期" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <label class="layui-form-label">结束日期</label>
                                <div class="layui-input-block">
                                    <input type="text" name="end_date" id="manual-end-date" placeholder="请选择结束日期" class="layui-input" readonly>
                                </div>
                            </div>
                            <div class="layui-form-item">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" id="confirm-manual-sync">开始同步</button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="cancel-manual-sync">取消</button>
                                </div>
                            </div>
                        </form>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '手动同步质量异常单',
                    area: ['400px', '300px'],
                    content: content,
                    btn: false,
                    success: function(layero, index) {
                        // 重新渲染表单
                        form.render();

                        // 初始化日期选择器
                        laydate.render({
                            elem: layero.find('#manual-start-date')[0],
                            type: 'date'
                        });

                        laydate.render({
                            elem: layero.find('#manual-end-date')[0],
                            type: 'date'
                        });

                        // 绑定确认同步按钮事件
                        layero.find('#confirm-manual-sync').on('click', function(){
                            var startDate = layero.find('#manual-start-date').val();
                            var endDate = layero.find('#manual-end-date').val();

                            if(!startDate || !endDate){
                                layer.msg('请选择开始和结束日期', {icon: 2});
                                return;
                            }

                            layer.close(index);
                            var loadingIndex = layer.load(1, {shade: [0.3, '#000']});

                            $.ajax({
                                url: '/system/dindin/api/quality-exceptions/manual-sync',
                                type: 'POST',
                                contentType: 'application/json',
                                data: JSON.stringify({
                                    start_date: startDate,
                                    end_date: endDate
                                }),
                                success: function(res){
                                    layer.close(loadingIndex);
                                    if(res.code === 0){
                                        layer.msg('手动同步成功', {icon: 1});
                                        tableIns.reload();
                                    } else {
                                        layer.msg(res.msg || '同步失败', {icon: 2});
                                    }
                                },
                                error: function(xhr, status, error){
                                    layer.close(loadingIndex);
                                    var errorMsg = '网络请求失败';
                                    if(xhr.responseJSON && xhr.responseJSON.msg){
                                        errorMsg = xhr.responseJSON.msg;
                                    } else if(xhr.responseText){
                                        try {
                                            var errorData = JSON.parse(xhr.responseText);
                                            errorMsg = errorData.msg || errorMsg;
                                        } catch(e) {
                                            errorMsg = '手动同步失败: ' + xhr.status + ' ' + xhr.statusText;
                                        }
                                    }
                                    layer.msg(errorMsg, {icon: 2});
                                }
                            });
                        });

                        // 绑定取消按钮事件
                        layero.find('#cancel-manual-sync').on('click', function(){
                            layer.close(index);
                        });
                    }
                });
            });



            // 刷新状态
            $('#refresh-status').on('click', function(){
                tableIns.reload();
                layer.msg('状态已刷新', {icon: 1});
            });

            // 显示异常单详情
            function showExceptionDetail(data) {
                var content = `
                    <div style="padding: 20px;">
                        <div class="layui-form">
                            <!-- 基本信息分组 -->
                            <fieldset class="layui-elem-field layui-field-title" style="margin-top: 0;">
                                <legend><i class="layui-icon layui-icon-user"></i> 基本信息</legend>
                            </fieldset>
                            <div class="layui-row layui-col-space15">
                                <!-- <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">异常单ID:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.id || '-'}</div>
                                        </div>
                                    </div>
                                </div> -->
                                <!-- <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">审批实例ID:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.process_instance_id || '-'}</div>
                                        </div>
                                    </div>
                                </div> -->
                            </div>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md12">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">标题:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.title || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">审批状态:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${getStatusText(data.approval_status)}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">发起部门:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.originator_dept_name || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">责任部门:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.responsible_dept_name || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <!-- <div class="layui-row layui-col-space15">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">责任人:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.responsible_person || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div> -->

                            <!-- 项目信息分组 -->
                            <fieldset class="layui-elem-field layui-field-title">
                                <legend><i class="layui-icon layui-icon-template"></i> 项目信息</legend>
                            </fieldset>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">项目类型:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.project_type || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">项目编号:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.project_code || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md4">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">机台编号:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.machine_number || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="layui-row layui-col-space15">
                                   <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">异常数量:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid" style="margin-right: 0px;">
                                                <span class="layui-badge layui-bg-orange">${data.exception_quantity || '-'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md3">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">预估工时:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid" style="margin-right: 0px;">
                                                <span class="layui-badge layui-bg-blue">${data.estimated_hours || '-'}</span> 小时
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label" style="text-align: center;">零件图号:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid" style="margin-right: 0px;">${data.part_drawing_number || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                             
                            </div>

                            <!-- 异常描述 -->
                            <fieldset class="layui-elem-field layui-field-title">
                                <legend><i class="layui-icon layui-icon-note"></i> 异常描述</legend>
                            </fieldset>
                            <div class="layui-form-item layui-form-text">
                                <div class="layui-input-block" style="margin-left: 30px;">
                                    <div style="padding: 10px; background: #f8f8f8; border-radius: 4px; min-height: 60px; line-height: 1.6;">
                                        ${data.exception_description || '暂无描述'}
                                    </div>
                                </div>
                            </div>

                            <!-- 时间信息分组 -->
                            <fieldset class="layui-elem-field layui-field-title">
                                <legend><i class="layui-icon layui-icon-time"></i> 时间信息</legend>
                            </fieldset>
                            <div class="layui-row layui-col-space15">
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">创建时间:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.create_time || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="layui-col-md6">
                                    <div class="layui-form-item">
                                        <label class="layui-form-label">完成时间:</label>
                                        <div class="layui-input-block">
                                            <div class="layui-form-mid">${data.finish_time || '-'}</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '质量异常单详情',
                    area: ['800px', '600px'],
                    content: content,
                    btn: ['关闭'],
                    btn1: function(index) {
                        layer.close(index);
                    }
                });
            }

            // 获取状态文本
            function getStatusText(status) {
                switch(status) {
                    case 'NEW': return '<span class="status-tag status-new">新建</span>';
                    case 'RUNNING': return '<span class="status-tag status-running">审批中</span>';
                    case 'COMPLETED': return '<span class="status-tag status-completed">已完成</span>';
                    case 'TERMINATED': return '<span class="status-tag status-terminated">已终止</span>';
                    case 'CANCELED': return '<span class="status-tag status-canceled">已取消</span>';
                    default: return '<span class="status-tag status-canceled">' + (status || '未知') + '</span>';
                }
            }

            // ==================== Excel导入功能 ====================

            // Excel导入按钮点击事件
            $('#excel-import').on('click', function(){
                showExcelImportModal();
            });

            // 模板下载按钮点击事件
            $('#download-template').on('click', function(){
                window.open('/system/dindin/api/quality-exceptions/template', '_blank');
            });

            // 显示Excel导入弹窗
            function showExcelImportModal() {
                var content = `
                    <div style="padding: 20px; width: 600px;">
                        <form class="layui-form" lay-filter="excel-import-form">
                            <!-- 文件上传区域 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">选择文件</label>
                                <div class="layui-input-block">
                                    <div class="layui-upload-drag" id="excel-upload-area" style="height: 120px;">
                                        <i class="layui-icon layui-icon-upload" style="font-size: 50px; color: #999;"></i>
                                        <p style="margin: 10px 0;">点击上传，或将Excel文件拖拽到此处</p>
                                        <p style="font-size: 12px; color: #999;">支持 .xlsx 和 .xls 格式，文件大小不超过10MB</p>
                                    </div>
                                    <div class="layui-hide" id="upload-preview">
                                        <hr>
                                        <div id="file-info"></div>
                                    </div>
                                </div>
                            </div>

                            <!-- 导入选项 -->
                            <div class="layui-form-item">
                                <label class="layui-form-label">导入模式</label>
                                <div class="layui-input-block">
                                    <input type="radio" name="import_mode" value="insert" title="仅新增（跳过重复）" checked>
                                    <input type="radio" name="import_mode" value="update" title="覆盖更新（更新重复）">
                                    <input type="radio" name="import_mode" value="upsert" title="新增或更新（智能处理）">
                                </div>
                            </div>

                            <!-- 数据预览 -->
                            <div class="layui-form-item" id="data-preview-section" style="display: none;">
                                <label class="layui-form-label">数据预览</label>
                                <div class="layui-input-block">
                                    <div class="data-preview-container">
                                        <table id="preview-table">
                                            <thead id="preview-header"></thead>
                                            <tbody id="preview-body"></tbody>
                                        </table>
                                    </div>
                                    <p style="font-size: 12px; color: #666; margin-top: 5px;">
                                        <span id="preview-summary"></span>
                                    </p>
                                </div>
                            </div>

                            <!-- 验证结果 -->
                            <div class="layui-form-item" id="validation-section" style="display: none;">
                                <label class="layui-form-label">验证结果</label>
                                <div class="layui-input-block">
                                    <div id="validation-summary" style="margin-bottom: 10px;"></div>
                                    <div id="validation-errors" style="max-height: 150px; overflow-y: auto;"></div>
                                    <div id="reselect-file-section" style="margin-top: 10px; display: none;">
                                        <button type="button" class="layui-btn layui-btn-normal layui-btn-sm" id="reselect-file-btn">
                                            <i class="layui-icon layui-icon-refresh"></i> 重新选择文件
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="layui-form-item" style="margin-top: 20px;">
                                <div class="layui-input-block">
                                    <button type="button" class="layui-btn" id="start-import" disabled>
                                        <i class="layui-icon layui-icon-upload"></i> 开始导入
                                    </button>
                                    <button type="button" class="layui-btn layui-btn-primary" id="cancel-import">
                                        <i class="layui-icon layui-icon-close"></i> 取消
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                `;

                layer.open({
                    type: 1,
                    title: '<i class="layui-icon layui-icon-upload" style="margin-right: 8px;"></i>Excel导入质量异常单',
                    area: ['700px', '600px'],
                    content: content,
                    success: function(layero, index) {
                        // 重新渲染表单
                        form.render();

                        // 先重置表单，再初始化文件上传
                        resetImportForm();
                        initFileUpload();

                        // 绑定取消按钮事件
                        layero.find('#cancel-import').on('click', function(){
                            layer.close(index);
                        });

                        // 绑定重新选择文件按钮事件
                        layero.find('#reselect-file-btn').on('click', function(){
                            resetImportForm();
                            initFileUpload();
                        });
                    }
                });
            }

            // 初始化文件上传
            function initFileUpload() {
                upload.render({
                    elem: '#excel-upload-area',
                    url: '/system/dindin/api/quality-exceptions/import',
                    accept: 'file',
                    exts: 'xlsx|xls',
                    size: 10240, // 10MB
                    auto: false,
                    bindAction: '#start-import',
                    choose: function(obj){
                        var files = obj.pushFile();

                        // 显示文件信息
                        obj.preview(function(index, file, result){
                            $('#file-info').html(`
                                <p><strong>文件名：</strong>${file.name}</p>
                                <p><strong>文件大小：</strong>${(file.size/1024/1024).toFixed(2)} MB</p>
                                <p><strong>文件类型：</strong>${file.type}</p>
                            `);
                            $('#upload-preview').removeClass('layui-hide');

                            // 验证数据
                            validateImportData(file);
                        });
                    },
                    data: function(){
                        return {
                            import_mode: $('input[name="import_mode"]:checked').val()
                        };
                    },
                    done: function(res){
                        if(res.success === true || res.code === 0){
                            var message = '导入完成';
                            if(res.data) {
                                var successCount = res.data.success_count || 0;
                                var failedCount = res.data.failed_count || 0;
                                var duplicateCount = res.data.duplicate_count || 0;

                                // 将重复数据计入失败数量
                                var totalFailedCount = failedCount + duplicateCount;

                                var details = [];
                                if(successCount > 0) details.push('成功 ' + successCount + ' 条');
                                if(totalFailedCount > 0) {
                                    var failedDetail = '失败 ' + totalFailedCount + ' 条';
                                    if(duplicateCount > 0) {
                                        failedDetail += '（数据重复 ' + duplicateCount + ' 条）';
                                    }
                                    details.push(failedDetail);
                                }

                                if(details.length > 0) {
                                    message += '：' + details.join('，');
                                }
                            }

                            // 优先使用后端返回的消息
                            if(res.msg && res.msg.trim() !== '') {
                                message = res.msg;
                            }

                            // 根据是否有成功数据决定图标
                            var iconType = (res.data && res.data.success_count > 0) ? 1 : 2;

                            layer.msg(message, {
                                icon: iconType,
                                time: 3000 // 3秒
                            });

                            layer.closeAll('page'); // 只关闭弹窗，不关闭消息提示
                            tableIns.reload(); // 刷新表格
                        } else {
                            layer.msg(res.msg || '导入失败', {
                                icon: 2,
                                time: 3000 // 3秒
                            });
                        }
                    },
                    error: function(xhr, status, error){
                        var errorMsg = '上传失败';
                        if(xhr.responseJSON && xhr.responseJSON.msg) {
                            errorMsg = xhr.responseJSON.msg;
                        } else if(xhr.responseText) {
                            try {
                                var errorData = JSON.parse(xhr.responseText);
                                errorMsg = errorData.msg || errorMsg;
                            } catch(e) {
                                errorMsg = '上传失败: ' + xhr.status + ' ' + xhr.statusText;
                            }
                        }

                        layer.msg(errorMsg, {
                            icon: 2,
                            time: 3000 // 3秒
                        });
                    }
                });
            }

            // 验证导入数据
            function validateImportData(file) {
                var formData = new FormData();
                formData.append('file', file);

                $.ajax({
                    url: '/system/dindin/api/quality-exceptions/validate',
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(res) {
                        if(res.success === true) {
                            showDataPreview(res.data);
                            showValidationResult(res.data.validation);

                            // 启用导入按钮
                            if(res.data.validation.valid) {
                                $('#start-import').prop('disabled', false);
                            }
                        } else {
                            // 验证失败时也显示验证结果，而不是直接弹出错误消息
                            if(res.data && res.data.validation) {
                                showDataPreview(res.data);
                                showValidationResult(res.data.validation);
                            } else {
                                layer.msg(res.msg || '数据验证失败', {
                                    icon: 2,
                                    time: 3000
                                });
                            }
                        }
                    },
                    error: function(xhr, status, error) {
                        layer.msg('数据验证失败', {
                            icon: 2,
                            time: 3000
                        });
                    }
                });
            }

            // 显示数据预览
            function showDataPreview(data) {
                var preview = data.preview;
                var totalCount = data.total_count;

                if(preview && preview.length > 0) {
                    // 构建表头
                    var headers = Object.keys(preview[0]);
                    var headerHtml = '<tr>' + headers.map(h => `<th>${h}</th>`).join('') + '</tr>';
                    $('#preview-header').html(headerHtml);

                    // 构建数据行
                    var bodyHtml = preview.map(row => {
                        return '<tr>' + headers.map(h => `<td>${row[h] || ''}</td>`).join('') + '</tr>';
                    }).join('');
                    $('#preview-body').html(bodyHtml);

                    $('#preview-summary').text(`共 ${totalCount} 条数据，预览前 ${preview.length} 条`);
                    $('#data-preview-section').show();
                }
            }

            // 显示验证结果
            function showValidationResult(validation) {
                var summaryHtml = '';
                var errorsHtml = '';

                if(validation.valid) {
                    summaryHtml = '<span style="color: #52c41a;"><i class="layui-icon layui-icon-ok"></i> 数据验证通过</span>';
                    $('#reselect-file-section').hide();
                } else {
                    summaryHtml = '<span style="color: #f5222d;"><i class="layui-icon layui-icon-close"></i> 数据验证失败</span>';

                    if(validation.errors && validation.errors.length > 0) {
                        errorsHtml = '<ul style="margin: 0; padding-left: 20px;">';
                        validation.errors.forEach(error => {
                            errorsHtml += `<li style="color: #f5222d; margin: 5px 0;">${error}</li>`;
                        });
                        errorsHtml += '</ul>';
                    }

                    // 验证失败时显示重新选择文件按钮
                    $('#reselect-file-section').show();
                }

                $('#validation-summary').html(summaryHtml);
                $('#validation-errors').html(errorsHtml);
                $('#validation-section').show();
            }

            // 重置导入表单
            function resetImportForm() {
                $('#upload-preview').addClass('layui-hide');
                $('#data-preview-section').hide();
                $('#validation-section').hide();
                $('#reselect-file-section').hide();
                $('#start-import').prop('disabled', true);
                $('#file-info').html('');

                // 重新创建文件上传区域的DOM，彻底清理upload组件状态
                var uploadAreaHtml = `
                    <div class="layui-upload-drag" id="excel-upload-area" style="height: 120px;">
                        <i class="layui-icon layui-icon-upload" style="font-size: 50px; color: #999;"></i>
                        <p style="margin: 10px 0;">点击上传，或将Excel文件拖拽到此处</p>
                        <p style="font-size: 12px; color: #999;">支持 .xlsx 和 .xls 格式，文件大小不超过10MB</p>
                    </div>
                `;
                $('#excel-upload-area').parent().html(uploadAreaHtml);

                form.render();
            }


        });
    </script>
</body>
</html>
