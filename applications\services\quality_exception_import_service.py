"""
质量异常单Excel导入服务
提供Excel文件导入、数据验证、模板生成等功能
"""

import datetime
import hashlib
import json
import pandas as pd
import time
from io import BytesIO
from typing import Dict, List, Optional
from flask_login import current_user
from applications.extensions import db
from applications.models import (
    QualityException, QualityExceptionImportLog,
    Dept, Import_project, ProjectManageDept, User
)
from applications.models.quality_exception_project_binding import QualityExceptionProjectBinding
from applications.services.enhanced_project_matcher import EnhancedProjectMatcher
from applications.services.project_code_parser import ProjectCodeParser
from applications.services.project_type_normalizer import ProjectTypeNormalizer
# 从Statistical_data模块导入get_subordinate_depts函数
def get_subordinate_depts(dept_id):
    """获取指定部门及其所有子部门"""
    from applications.models import Dept
    depts = Dept.query.order_by(Dept.sort).all()
    result = [dept_id]

    def find_children(parent_id):
        for dept in depts:
            if dept.parent_id == parent_id:
                result.append(dept.id)
                find_children(dept.id)

    find_children(dept_id)
    return result


class QualityExceptionImportService:
    """质量异常单Excel导入服务"""
    
    def __init__(self):
        self.required_fields = [
            '异常单标题', '项目类型', '项目编号', '预估工时',
            '发起人', '发起部门', '责任部门', '异常描述'
        ]
        self.optional_fields = [
            '机台编号', '零件图号', '料件成本', '责任人', '严重程度', '备注'
        ]
        # 初始化项目匹配器
        self.project_matcher = EnhancedProjectMatcher()
        # 初始化项目编号解析器
        self.project_parser = ProjectCodeParser()
        # 初始化项目类型标准化器
        self.type_normalizer = ProjectTypeNormalizer()
        self.field_mapping = {
            '异常单标题': 'title',
            '项目类型': 'project_type',
            '项目编号': 'project_code',
            '预估工时': 'estimated_hours',
            '发起人': 'originator_name',
            '发起部门': 'originator_dept_name',
            '责任部门': 'responsible_dept_name',
            '异常描述': 'exception_description',
            '机台编号': 'machine_number',
            '零件图号': 'part_drawing_number',
            '料件成本': 'cost_impact',
            '责任人': 'responsible_person',
            '严重程度': 'severity_level',
            '备注': 'remark'
        }
        self.max_file_size = 10 * 1024 * 1024  # 10MB
        self.batch_size = 500
    
    def validate_file(self, file) -> Dict:
        """文件格式验证"""
        try:
            # 检查文件名
            if not file.filename:
                return {'valid': False, 'message': '未选择文件'}
            
            # 检查文件扩展名
            if not file.filename.lower().endswith(('.xlsx', '.xls')):
                return {'valid': False, 'message': '仅支持Excel文件(.xlsx/.xls)'}
            
            # 检查文件大小
            file.seek(0, 2)  # 移动到文件末尾
            file_size = file.tell()
            file.seek(0)  # 重置文件指针
            
            if file_size > self.max_file_size:
                return {'valid': False, 'message': f'文件大小超过限制({self.max_file_size/1024/1024:.1f}MB)'}
            
            if file_size == 0:
                return {'valid': False, 'message': '文件为空'}
            
            return {'valid': True, 'file_size': file_size}
            
        except Exception as e:
            return {'valid': False, 'message': f'文件验证失败: {str(e)}'}
    
    def validate_data(self, df: pd.DataFrame, user_id: int) -> Dict:
        """数据内容验证"""
        errors = []
        warnings = []
        
        try:
            # 检查是否为空
            if df.empty:
                return {'valid': False, 'errors': ['Excel文件为空']}
            
            # 检查必填字段是否存在
            missing_fields = [field for field in self.required_fields if field not in df.columns]
            if missing_fields:
                errors.append(f'缺少必填字段: {", ".join(missing_fields)}')
            
            # 逐行验证数据
            for index, row in df.iterrows():
                row_num = index + 2  # Excel行号（从第2行开始）
                
                # 验证必填字段
                for field in self.required_fields:
                    if field in df.columns:
                        value = row[field]
                        if pd.isna(value) or str(value).strip() == '':
                            errors.append(f'第{row_num}行：{field} 不能为空')
                
                # 验证数据格式
                if '预估工时' in df.columns and not pd.isna(row['预估工时']):
                    try:
                        hours = float(row['预估工时'])
                        if hours < 0 or hours > 999.99:
                            errors.append(f'第{row_num}行：预估工时必须在0-999.99之间')
                    except (ValueError, TypeError):
                        errors.append(f'第{row_num}行：预估工时格式不正确')
                
                # 验证料件成本
                if '料件成本' in df.columns and not pd.isna(row['料件成本']):
                    try:
                        cost = float(row['料件成本'])
                        if cost < 0:
                            errors.append(f'第{row_num}行：料件成本不能为负数')
                    except (ValueError, TypeError):
                        errors.append(f'第{row_num}行：料件成本格式不正确')
            
            # 验证业务逻辑
            business_validation = self._validate_business_logic(df)
            errors.extend(business_validation.get('errors', []))
            warnings.extend(business_validation.get('warnings', []))
            
            # 权限验证
            permission_validation = self._validate_permissions(df, user_id)
            if not permission_validation['valid']:
                errors.append(permission_validation['message'])
            
            return {
                'valid': len(errors) == 0,
                'errors': errors,
                'warnings': warnings,
                'total_count': len(df)
            }
            
        except Exception as e:
            return {'valid': False, 'errors': [f'数据验证失败: {str(e)}']}
    
    def _validate_business_logic(self, df: pd.DataFrame) -> Dict:
        """业务逻辑验证"""
        errors = []
        warnings = []

        try:
            # 验证项目类型是否存在
            if '项目类型' in df.columns:
                project_types = df['项目类型'].dropna().unique()
                existing_types = {dept.dept_name for dept in ProjectManageDept.query.all()}

                for project_type in project_types:
                    # 尝试从复合格式中提取项目类型
                    extracted_type = self._extract_project_type_from_input(project_type)

                    # 标准化项目类型
                    normalization_result = self.type_normalizer.normalize_project_type(extracted_type)
                    normalized_type = normalization_result.normalized_type if normalization_result.normalized_type else extracted_type

                    # 检查标准化后的类型是否存在
                    if normalized_type not in existing_types:
                        errors.append(f'项目类型 "{project_type}" 在系统中不存在')
            
            # 验证项目编号是否存在
            if '项目编号' in df.columns:
                project_codes = df['项目编号'].dropna().unique()
                existing_codes = {proj.project_code for proj in Import_project.query.all()}
                
                for project_code in project_codes:
                    if project_code not in existing_codes:
                        warnings.append(f'项目编号 "{project_code}" 在系统中不存在，将创建新项目关联')
            
            # 验证部门是否存在
            dept_fields = ['发起部门', '责任部门']
            for field in dept_fields:
                if field in df.columns:
                    dept_names = df[field].dropna().unique()
                    existing_depts = {dept.dept_name for dept in Dept.query.filter_by(status=1).all()}
                    
                    for dept_name in dept_names:
                        if dept_name not in existing_depts:
                            errors.append(f'{field} "{dept_name}" 在系统中不存在')
            
            return {'errors': errors, 'warnings': warnings}
            
        except Exception as e:
            return {'errors': [f'业务逻辑验证失败: {str(e)}'], 'warnings': []}

    def _extract_project_type_from_input(self, input_value: str) -> str:
        """
        从输入值中提取项目类型

        Args:
            input_value: 输入的项目类型值，可能是纯类型或复合格式

        Returns:
            str: 提取的项目类型
        """
        if not input_value or not isinstance(input_value, str):
            return input_value

        input_value = input_value.strip()

        # 如果输入值看起来像复合格式（包含数字），尝试解析
        if any(char.isdigit() for char in input_value):
            try:
                # 使用项目编号解析器解析
                parsed_result = self.project_parser.parse_project_code(input_value)

                # 如果是前缀格式，从notes中提取项目类型
                if parsed_result.format_type == 'prefix' and parsed_result.notes:
                    # 从notes中提取项目类型，格式如："前缀格式解析: GS2425 -> 项目类型:GS, 编号:2425"
                    import re
                    type_match = re.search(r'项目类型:([^,]+)', parsed_result.notes)
                    if type_match:
                        return type_match.group(1).strip()

                # 如果是多项目格式且包含前缀，尝试从第一个项目中提取类型
                if parsed_result.format_type == 'multi_project' and parsed_result.parsed_codes:
                    # 尝试解析第一个项目编号
                    first_code = parsed_result.parsed_codes[0]
                    # 从原始输入中找到对应的前缀部分
                    for part in input_value.split('/'):
                        part = part.strip()
                        if first_code in part:
                            # 提取前缀部分
                            prefix = part.replace(first_code, '').strip()
                            if prefix:
                                return prefix

            except Exception as e:
                print(f"解析项目类型时发生错误: {str(e)}")

        # 如果无法解析或不是复合格式，直接返回原值
        return input_value
    
    def _validate_permissions(self, df: pd.DataFrame, user_id: int) -> Dict:
        """权限验证"""
        try:
            # 管理员拥有全部权限
            if current_user.username == 'admin':
                return {'valid': True}
            
            # 获取用户可访问的部门
            user = User.query.get(user_id)
            if not user or not user.dept_id:
                return {'valid': False, 'message': '用户未分配部门'}
            
            accessible_dept_ids = get_subordinate_depts(user.dept_id)
            accessible_dept_names = {dept.dept_name for dept in 
                                   Dept.query.filter(Dept.id.in_(accessible_dept_ids)).all()}
            
            # 检查发起部门和责任部门权限
            dept_fields = ['发起部门', '责任部门']
            for field in dept_fields:
                if field in df.columns:
                    dept_names = set(df[field].dropna().unique())
                    unauthorized_depts = dept_names - accessible_dept_names
                    
                    if unauthorized_depts:
                        return {
                            'valid': False,
                            'message': f'无权限导入以下部门数据: {", ".join(unauthorized_depts)}'
                        }
            
            return {'valid': True}
            
        except Exception as e:
            return {'valid': False, 'message': f'权限验证失败: {str(e)}'}
    
    def calculate_file_hash(self, file) -> str:
        """计算文件MD5哈希值"""
        file.seek(0)
        file_content = file.read()
        file.seek(0)
        return hashlib.md5(file_content).hexdigest()

    def process_import(self, file, user_id: int, import_mode: str = 'insert') -> Dict:
        """处理Excel导入"""
        import_log = None

        try:
            # 文件验证
            file_validation = self.validate_file(file)
            if not file_validation['valid']:
                return file_validation

            # 计算文件哈希
            file_hash = self.calculate_file_hash(file)

            # 创建导入日志
            user = User.query.get(user_id)
            import_log = QualityExceptionImportLog.create_log(
                user_id=user_id,
                user_name=user.realname if user else 'Unknown',
                file_name=file.filename,
                file_size=file_validation['file_size'],
                file_hash=file_hash,
                import_mode=import_mode
            )

            # 读取Excel数据
            df = pd.read_excel(file, dtype=str)  # 全部读取为字符串，后续转换

            # 数据验证
            validation_result = self.validate_data(df, user_id)
            if not validation_result['valid']:
                import_log.update_status('failed',
                                       validation_errors=json.dumps(validation_result['errors'], ensure_ascii=False),
                                       error_message='数据验证失败')
                return validation_result

            # 数据转换和导入
            import_result = self._execute_import(df, import_mode, user_id, import_log.id)

            # 更新导入日志
            import_log.update_status(
                'completed',
                total_count=len(df),
                success_count=import_result['success_count'],
                failed_count=import_result['failed_count'],
                duplicate_count=import_result.get('duplicate_count', 0)
            )

            # 生成详细的导入结果消息
            success_count = import_result["success_count"]
            failed_count = import_result["failed_count"]
            duplicate_count = import_result.get("duplicate_count", 0)

            # 将重复数据计入失败数量
            total_failed = failed_count + duplicate_count

            message_parts = []
            if success_count > 0:
                message_parts.append(f'成功 {success_count} 条')
            if total_failed > 0:
                if duplicate_count > 0:
                    message_parts.append(f'失败 {total_failed} 条（数据重复 {duplicate_count} 条）')
                else:
                    message_parts.append(f'失败 {total_failed} 条')

            if not message_parts:
                message = '导入完成：无数据处理'
            else:
                message = f'导入完成：{", ".join(message_parts)}'

            return {
                'valid': True,
                'message': message,
                'data': import_result,
                'import_log_id': import_log.id
            }

        except Exception as e:
            if import_log:
                import_log.update_status('failed', error_message=str(e))

            return {
                'valid': False,
                'message': f'导入失败: {str(e)}',
                'import_log_id': import_log.id if import_log else None
            }

    def _execute_import(self, df: pd.DataFrame, import_mode: str, user_id: int, log_id: int) -> Dict:
        """执行数据导入"""
        success_count = 0
        failed_count = 0
        duplicate_count = 0
        errors = []

        try:
            # 分批处理数据
            for start_idx in range(0, len(df), self.batch_size):
                end_idx = min(start_idx + self.batch_size, len(df))
                batch_df = df.iloc[start_idx:end_idx]

                batch_result = self._process_batch(batch_df, import_mode, user_id)
                success_count += batch_result['success_count']
                failed_count += batch_result['failed_count']
                duplicate_count += batch_result['duplicate_count']
                errors.extend(batch_result['errors'])

                # 批次间添加短暂延迟
                time.sleep(0.1)

            return {
                'success_count': success_count,
                'failed_count': failed_count,
                'duplicate_count': duplicate_count,
                'errors': errors
            }

        except Exception as e:
            return {
                'success_count': success_count,
                'failed_count': failed_count + (len(df) - success_count - failed_count),
                'duplicate_count': duplicate_count,
                'errors': errors + [f'批量导入失败: {str(e)}']
            }

    def _process_batch(self, batch_df: pd.DataFrame, import_mode: str, user_id: int) -> Dict:
        """处理单个批次的数据"""
        success_count = 0
        failed_count = 0
        duplicate_count = 0
        errors = []

        for index, row in batch_df.iterrows():
            try:
                row_num = index + 2  # Excel行号

                # 转换数据
                exception_data = self._convert_row_to_exception(row, user_id)

                # 检查重复
                existing = self._check_duplicate(exception_data)

                if existing and import_mode == 'insert':
                    duplicate_count += 1
                    continue
                elif existing and import_mode == 'update':
                    self._update_existing_exception(existing, exception_data)
                    success_count += 1
                elif existing and import_mode == 'upsert':
                    self._update_existing_exception(existing, exception_data)
                    success_count += 1
                else:
                    # 创建新记录
                    self._create_new_exception(exception_data)
                    success_count += 1

            except Exception as e:
                failed_count += 1
                errors.append(f'第{row_num}行导入失败: {str(e)}')

        # 提交批次
        try:
            db.session.commit()
        except Exception as e:
            db.session.rollback()
            # 如果批次提交失败，将成功数归零，失败数设为批次总数
            failed_count = len(batch_df)
            success_count = 0
            errors.append(f'批次提交失败: {str(e)}')

        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'duplicate_count': duplicate_count,
            'errors': errors
        }

    def _convert_row_to_exception(self, row: pd.Series, user_id: int) -> Dict:
        """将Excel行数据转换为质量异常单数据"""
        data = {}

        # 基本字段映射
        for excel_field, db_field in self.field_mapping.items():
            if excel_field in row.index and not pd.isna(row[excel_field]):
                value = str(row[excel_field]).strip()
                if value:
                    data[db_field] = value

        # 数据类型转换
        if 'estimated_hours' in data:
            try:
                data['estimated_hours'] = float(data['estimated_hours'])
            except (ValueError, TypeError):
                data['estimated_hours'] = 0.0

        if 'cost_impact' in data:
            try:
                data['cost_impact'] = float(data['cost_impact'])
            except (ValueError, TypeError):
                data['cost_impact'] = None

        # 部门ID转换
        if 'originator_dept_name' in data:
            dept = Dept.query.filter_by(dept_name=data['originator_dept_name'], status=1).first()
            if dept:
                data['originator_local_dept_id'] = dept.id

        if 'responsible_dept_name' in data:
            dept = Dept.query.filter_by(dept_name=data['responsible_dept_name'], status=1).first()
            if dept:
                data['responsible_dept_id'] = dept.id

        # 项目ID转换
        if 'project_code' in data:
            try:
                project = Import_project.query.filter_by(project_code=data['project_code']).first()
                if project:
                    data['project_id'] = project.id
                    print(f"成功找到项目: {data['project_code']} -> ID: {project.id}")
                else:
                    print(f"未找到项目编号: {data['project_code']}")
            except Exception as e:
                print(f"查询项目时发生错误: {str(e)}, 项目编号: {data.get('project_code', 'N/A')}")
                # 不中断导入流程，继续处理其他数据

        # 设置导入相关字段
        data['import_source'] = 'excel'
        data['import_user_id'] = user_id
        data['import_time'] = datetime.datetime.now()

        # 设置默认值
        data['process_instance_id'] = f"EXCEL_{int(time.time() * 1000)}_{user_id}"
        data['originator_userid'] = f"excel_user_{user_id}"
        data['approval_status'] = 'COMPLETED'
        data['create_time'] = datetime.datetime.now()
        data['sync_time'] = datetime.datetime.now()
        data['sync_status'] = 1

        # 删除不属于QualityException模型的临时字段
        # 注意：originator_dept_name 和 responsible_dept_name 都是模型字段，不应删除
        # temp_fields_to_remove = []
        # for field in temp_fields_to_remove:
        #     if field in data:
        #         del data[field]

        return data

    def _check_duplicate(self, exception_data: Dict) -> Optional[QualityException]:
        """检查重复记录"""
        # 基于标题和项目编号检查重复
        title = exception_data.get('title')
        project_code = exception_data.get('project_code')

        if title and project_code:
            return QualityException.query.filter_by(
                title=title,
                project_code=project_code,
                import_source='excel'
            ).first()

        return None

    def _create_new_exception(self, exception_data: Dict):
        """创建新的质量异常单"""
        exception = QualityException(**exception_data)
        db.session.add(exception)
        db.session.flush()  # 刷新会话以获取异常单ID

        # 进行项目绑定
        self._bind_projects_to_exception(exception, exception_data)

    def _update_existing_exception(self, existing: QualityException, exception_data: Dict):
        """更新现有的质量异常单"""
        # 更新允许修改的字段
        updatable_fields = [
            'title', 'project_type', 'estimated_hours', 'originator_name',
            'originator_dept_name', 'originator_local_dept_id', 'responsible_dept_id',
            'exception_description', 'machine_number', 'part_drawing_number',
            'cost_impact', 'responsible_person', 'severity_level', 'remark'
        ]

        for field in updatable_fields:
            if field in exception_data:
                setattr(existing, field, exception_data[field])

        # 更新导入信息
        existing.import_time = datetime.datetime.now()

        # 更新项目绑定
        self._update_projects_binding_for_exception(existing, exception_data)

    def _bind_projects_to_exception(self, exception: QualityException, exception_data: Dict):
        """
        为质量异常单绑定项目（Excel导入场景）

        Args:
            exception: 质量异常单对象
            exception_data: 异常单数据
        """
        if not exception.project_code:
            print("项目编号为空，跳过项目绑定")
            return

        try:
            print(f"开始项目绑定: 异常单ID={exception.id}, 项目编号={exception.project_code}, 项目类型={exception.project_type}")

            # 使用增强匹配器进行项目匹配
            match_result = self.project_matcher.match_projects(
                project_code=exception.project_code,
                project_type=exception.project_type
            )

            print(f"项目匹配完成:")
            print(f"  - 总体置信度: {match_result.total_confidence:.2f}")
            print(f"  - 匹配项目数: {len(match_result.matches)}")
            print(f"  - 未匹配编号数: {len(match_result.unmatched_codes)}")
            print(f"  - 详细说明: {match_result.notes}")

            # 如果有匹配的项目，创建绑定记录
            if match_result.matches:
                # 设置主项目ID（第一个匹配的项目）
                primary_match = match_result.matches[0]
                exception.project_id = primary_match.project_id
                print(f"设置主项目: ID={primary_match.project_id}, 名称={primary_match.project_name}, 置信度={primary_match.confidence:.2f}")

                # 为每个匹配的项目创建绑定记录
                for i, match in enumerate(match_result.matches):
                    binding = QualityExceptionProjectBinding.create_binding(
                        quality_exception_id=exception.id,
                        project_id=match.project_id,
                        binding_type='auto',
                        binding_source='excel_import',
                        match_confidence=match.confidence,
                        match_method=match.match_type,
                        original_project_code=exception.project_code,
                        parsed_project_code=match.project_code,
                        project_type_matched=match.project_type_name,
                        notes=match.notes,
                        match_details=json.dumps({
                            'original_code': match_result.original_code,
                            'parsed_codes': match_result.parsed_result.parsed_codes,
                            'format_type': match_result.parsed_result.format_type,
                            'total_confidence': match_result.total_confidence,
                            'match_index': i + 1,
                            'is_primary': i == 0
                        }, ensure_ascii=False)
                    )

                    db.session.add(binding)
                    print(f"创建项目绑定 #{i+1}: 异常{exception.id} -> 项目{match.project_id} ({match.project_name})")
                    print(f"  - 匹配方式: {match.match_type}")
                    print(f"  - 置信度: {match.confidence:.2f}")
                    print(f"  - 说明: {match.notes}")
            else:
                print(f"❌ 项目绑定失败: 未找到匹配的项目")
                print(f"  - 原始编号: {exception.project_code}")
                print(f"  - 项目类型: {exception.project_type}")
                print(f"  - 失败原因: {match_result.notes}")

                # 记录绑定失败的信息
                failed_binding = QualityExceptionProjectBinding.create_binding(
                    quality_exception_id=exception.id,
                    project_id=None,
                    binding_type='auto',
                    binding_source='excel_import',
                    match_confidence=0.0,
                    match_method='no_match',
                    original_project_code=exception.project_code,
                    parsed_project_code=None,
                    project_type_matched=exception.project_type,
                    notes=f"Excel导入自动匹配失败: {match_result.notes}",
                    match_details=json.dumps({
                        'original_code': match_result.original_code,
                        'parsed_codes': match_result.parsed_result.parsed_codes,
                        'format_type': match_result.parsed_result.format_type,
                        'total_confidence': match_result.total_confidence,
                        'failure_reason': 'no_match'
                    }, ensure_ascii=False),
                    is_active=False  # 失败的绑定记录设为非活跃状态
                )
                db.session.add(failed_binding)
                print(f"记录绑定失败信息: 异常{exception.id}")

        except Exception as e:
            print(f"❌ 项目绑定过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def _update_projects_binding_for_exception(self, exception: QualityException, exception_data: Dict):
        """
        更新质量异常单的项目绑定（Excel导入更新场景）

        Args:
            exception: 质量异常单对象
            exception_data: 异常单数据
        """
        if not exception.project_code:
            print("项目编号为空，跳过项目绑定更新")
            return

        try:
            print(f"开始更新项目绑定: 异常单ID={exception.id}, 项目编号={exception.project_code}")

            # 获取当前绑定的项目ID列表
            current_bindings = QualityExceptionProjectBinding.query.filter_by(
                quality_exception_id=exception.id,
                is_active=True
            ).all()
            current_project_ids = {binding.project_id for binding in current_bindings if binding.project_id}

            # 使用增强匹配器进行项目匹配
            match_result = self.project_matcher.match_projects(
                project_code=exception.project_code,
                project_type=exception.project_type
            )

            print(f"项目匹配完成:")
            print(f"  - 总体置信度: {match_result.total_confidence:.2f}")
            print(f"  - 匹配项目数: {len(match_result.matches)}")
            print(f"  - 当前绑定项目数: {len(current_project_ids)}")

            if match_result.matches:
                # 更新主项目ID
                primary_match = match_result.matches[0]
                exception.project_id = primary_match.project_id

                # 获取新匹配的项目ID列表
                new_project_ids = {match.project_id for match in match_result.matches}

                # 删除不再匹配的绑定记录
                for binding in current_bindings:
                    if binding.project_id not in new_project_ids:
                        binding.is_active = False
                        binding.updated_at = datetime.datetime.now()
                        print(f"停用绑定记录: 异常{exception.id} -> 项目{binding.project_id}")

                # 创建新的绑定
                for match in match_result.matches:
                    if match.project_id not in current_project_ids:
                        binding = QualityExceptionProjectBinding.create_binding(
                            quality_exception_id=exception.id,
                            project_id=match.project_id,
                            binding_type='auto',
                            binding_source='excel_update',
                            match_confidence=match.confidence,
                            match_method=match.match_type,
                            original_project_code=exception.project_code,
                            parsed_project_code=match.project_code,
                            project_type_matched=match.project_type_name,
                            notes=match.notes,
                            match_details=json.dumps({
                                'original_code': match_result.original_code,
                                'parsed_codes': match_result.parsed_result.parsed_codes,
                                'format_type': match_result.parsed_result.format_type,
                                'total_confidence': match_result.total_confidence
                            }, ensure_ascii=False)
                        )

                        db.session.add(binding)
                        print(f"创建新绑定记录: 异常{exception.id} -> 项目{match.project_id} ({match.project_name})")
            else:
                # 如果没有匹配项目，停用所有现有绑定
                for binding in current_bindings:
                    binding.is_active = False
                    binding.updated_at = datetime.datetime.now()

                # 清除主项目ID
                exception.project_id = None
                print(f"未找到匹配项目，清除所有绑定: 异常{exception.id}")

        except Exception as e:
            print(f"❌ 更新项目绑定过程中发生错误: {str(e)}")
            import traceback
            traceback.print_exc()

    def generate_template(self) -> BytesIO:
        """生成Excel导入模板"""
        # 示例数据
        template_data = {
            '异常单标题': ['产品质量异常-尺寸偏差', '设备故障异常', ''],
            '项目类型': ['机械加工', '设备维护', ''],
            '项目编号': ['MJ2024001', 'SB2024002', ''],
            '预估工时': [8.5, 4.0, ''],
            '发起人': ['张三', '王五', ''],
            '发起部门': ['质量部', '设备部', ''],
            '责任部门': ['生产部', '维修部', ''],
            '异常描述': ['产品尺寸超出公差范围，需要返工处理', '设备运行异常，影响生产进度', ''],
            '机台编号': ['CNC001', 'MILL002', ''],
            '零件图号': ['DWG-001', '', ''],
            '料件成本': [1500.00, 800.00, ''],
            '责任人': ['李四', '赵六', ''],
            '严重程度': ['高', '中', ''],
            '备注': ['紧急处理', '', '']
        }

        df = pd.DataFrame(template_data)

        output = BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            # 主数据表
            df.to_excel(writer, sheet_name='质量异常单导入模板', index=False)

            # 说明表
            instructions_data = {
                '字段名': list(template_data.keys()),
                '是否必填': ['是'] * 8 + ['否'] * 6,
                '数据类型': ['文本', '文本', '文本', '数字', '文本', '文本', '文本', '文本',
                           '文本', '文本', '数字', '文本', '文本', '文本'],
                '字段说明': [
                    '异常单的标题描述，不超过255字符',
                    '项目所属类型，需在系统项目类型中存在',
                    '项目编号，需在系统项目表中存在',
                    '预估处理工时，单位：小时，支持小数',
                    '异常单发起人姓名，不超过50字符',
                    '发起人所在部门，需在系统部门表中存在',
                    '负责处理的部门，需在系统部门表中存在',
                    '异常情况的详细描述，不超过1000字符',
                    '相关机台设备编号，可选填',
                    '零件图纸编号，可选填',
                    '预估料件成本，单位：元，支持小数',
                    '具体责任人姓名，可选填',
                    '异常严重程度：高/中/低，可选填',
                    '其他备注信息，可选填'
                ],
                '示例值': [
                    '产品质量异常-尺寸偏差',
                    '机械加工',
                    'MJ2024001',
                    '8.5',
                    '张三',
                    '质量部',
                    '生产部',
                    '产品尺寸超出公差范围，需要返工处理',
                    'CNC001',
                    'DWG-001',
                    '1500.00',
                    '李四',
                    '高',
                    '紧急处理'
                ]
            }

            instructions_df = pd.DataFrame(instructions_data)
            instructions_df.to_excel(writer, sheet_name='填写说明', index=False)

            # 设置列宽
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width

        output.seek(0)
        return output
